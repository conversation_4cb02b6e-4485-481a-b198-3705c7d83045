<?xml version="1.0" encoding="UTF-8" standalone="no"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.skyhigh.automation</groupId>
  <artifactId>qa-skyhigh-ui-playwright</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>com.skyhigh.automation</name>
   <properties><project.build.sourceEncoding>UTF-8</project.build.sourceEncoding></properties>
 	<profiles>
		<profile>
			<id>chrome-headless</id>
			<properties>
				<browser>chrome</browser>
				<headless>true</headless>
			</properties>
		</profile>
		<profile>
			<id>edge-headless</id>
			<properties>
				<browser>edge</browser>
				<headless>true</headless>
			</properties>
		</profile>
		<profile>
			<id>firefox-headless</id>
			<properties>
				<browser>firefox</browser>
				<headless>true</headless>
			</properties>
		</profile>
		<profile>
			<id>chrome-headmode</id>
			<properties>
				<browser>chrome</browser>
				<headless>false</headless>
			</properties>
		</profile>
		<profile>
			<id>edge-headmode</id>
			<properties>
				<browser>edge</browser>
				<headless>false</headless>
			</properties>
		</profile>

		<profile>
			<id>firefox-headmode</id>
			<properties>
				<browser>firefox</browser>
				<headless>false</headless>
			</properties>
		</profile>
	</profiles> 
    <dependencies>
		<dependency>
			<groupId>org.zaproxy</groupId>
			<artifactId>zap-clientapi</artifactId>
			<version>1.10.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.12.0</version> <!-- or the latest version -->
		</dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.16</version>
        </dependency>
        <dependency>
	        <groupId>org.slf4j</groupId>
	        <artifactId>slf4j-simple</artifactId>
	        <version>2.0.16</version> 
	    </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-java</artifactId>
            <version>7.18.0</version>
        </dependency>
		<dependency>
			<groupId>info.cukes</groupId>
			<artifactId>cucumber-picocontainer</artifactId>
			<version>1.2.6</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>3.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.microsoft.playwright</groupId>
			<artifactId>playwright</artifactId>
			<version>1.49.0</version>
		</dependency>
		<dependency>
			<groupId>io.cucumber</groupId>
			<artifactId>cucumber-picocontainer</artifactId>
			<version>7.12.1</version>
			<scope>test</scope>
		</dependency>    
        	<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.17.1</version>
		</dependency>
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-all</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>io.cucumber</groupId>
			<artifactId>cucumber-expressions</artifactId>
			<version>16.1.2</version>
		</dependency>   
        <dependency>
            <groupId>com.aventstack</groupId>
            <artifactId>extentreports</artifactId>
            <version>4.1.3</version>
        </dependency>
		<dependency>
			<groupId>org.assertj</groupId>
			<artifactId>assertj-core</artifactId>
			<version>3.24.2</version>
			<scope>test</scope>
		</dependency>		
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.10.2</version>
            <scope>test</scope>
        </dependency>

        <dependency>
			<groupId>io.cucumber</groupId>
			<artifactId>cucumber-junit</artifactId>
			<version>7.18.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>ru.yandex.qatools.ashot</groupId>
			<artifactId>ashot</artifactId>
			<version>1.5.4</version>
		</dependency>
        <dependency>
		    <groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-core</artifactId>
		    <version>2.19.0</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-api</artifactId>
		    <version>2.19.0</version>
		</dependency>
        <dependency>
            <groupId>io.github.bonigarcia</groupId>
            <artifactId>webdrivermanager</artifactId>
            <version>5.5.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>5.7.1</version>
		</dependency>
    </dependencies>
      <build>
		  
		  <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>/drivers/**</exclude>
                </excludes>
            </testResource>
        </testResources>
        <plugins>
			<!-- package test files into a jar  -->
		    <plugin>
		        <groupId>org.apache.maven.plugins</groupId>
		        <artifactId>maven-jar-plugin</artifactId>
		        <version>3.2.0</version>
		        <executions>
		          <execution>
		            <goals>
		              <goal>test-jar</goal>
		            </goals>
		          </execution>
		        </executions>
		    </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
			 <plugin>
			    <groupId>org.apache.maven.plugins</groupId>
			    <artifactId>maven-surefire-plugin</artifactId>
			    <version>2.22.0</version>
			    <configuration>
			        <testClassesDirectory>${project.build.testOutputDirectory}</testClassesDirectory>
			        <includes>
			            <include>**/TestRunner.java</include>
			        </includes>
			        <systemPropertyVariables>
			            <cucumber.filter.tags>${cucumber.filter.tags}</cucumber.filter.tags>
			            <accountnumber>${accountnumber}</accountnumber>
			            <env>${env}</env>
			            <serviceNumber>${serviceNumber}</serviceNumber>
			            <browser>${browser}</browser>
			            <trace>${trace}</trace>
			            <tenant>${tenant}</tenant>
			            <password>${password}</password>
			        </systemPropertyVariables>
			    </configuration>
			    <dependencies>
			        <dependency>
			            <groupId>org.apache.maven.surefire</groupId>
			            <artifactId>surefire-junit4</artifactId>
			            <version>2.22.0</version>
			        </dependency>
			    </dependencies>
			</plugin>
			
        </plugins>				
    </build>
</project>

