package com.customer;

import java.io.Serializable;

public class MedicareDetails implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	String medicareNumber;
	String individualNameNumber;
	String middleNameInitial;
	String colorOfCard;
	String cardExpiryMonth;
	String cardExpiryYear;

	public String getMedicareNumber() {
		return medicareNumber;
	}

	public void setMedicareNumber(String medicareNumber) {
		this.medicareNumber = medicareNumber;
	}

	public String getIndividualNameNumber() {
		return individualNameNumber;
	}

	public void setIndividualNameNumber(String individualNameNumber) {
		this.individualNameNumber = individualNameNumber;
	}

	public String getMiddleNameInitial() {
		return middleNameInitial;
	}

	public void setMiddleNameInitial(String middleNameInitial) {
		this.middleNameInitial = middleNameInitial;
	}

	public String getColorOfCard() {
		return colorOfCard;
	}

	public void setColorOfCard(String colorOfCard) {
		this.colorOfCard = colorOfCard;
	}

	public String getCardExpiryMonth() {
		return cardExpiryMonth;
	}

	public void setCardExpiryMonth(String cardExpiryMonth) {
		this.cardExpiryMonth = cardExpiryMonth;
	}

	public String getCardExpiryYear() {
		return cardExpiryYear;
	}

	public void setCardExpiryYear(String cardExpiryYear) {
		this.cardExpiryYear = cardExpiryYear;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	

}
