 package com.customer;

import java.io.Serializable;
import java.util.List;

public class TestData implements Serializable{

	private static final long serialVersionUID = 1L;
	String scenario;
	List<Data> data;

	public String getScenario() {
		return scenario;
	}

	public void setScenario(String scenario) {
		this.scenario = scenario;
	}

	public List<Data> getData() {
		return data;
	}

	public void setData(List<Data> data) {
		this.data = data;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	
	

}
