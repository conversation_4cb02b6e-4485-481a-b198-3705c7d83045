package com.customer;

import java.io.Serializable;

public class SecondaryContactDetails implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	String secondaryContactRelationship;
	String secondaryContactTitle;
	String secondaryContactFirstName;
	String secondaryContactLastName;
	String secondaryContactPhoneNumber;
	String secondaryContactMobilePhoneNumber;
	String secondaryContactEmail;
	String secondaryContactDob;

	public String getSecondaryContactRelationship() {
		return secondaryContactRelationship;
	}

	public void setSecondaryContactRelationship(String secondaryContactRelationship) {
		this.secondaryContactRelationship = secondaryContactRelationship;
	}

	public String getSecondaryContactTitle() {
		return secondaryContactTitle;
	}

	public void setSecondaryContactTitle(String secondaryContactTitle) {
		this.secondaryContactTitle = secondaryContactTitle;
	}

	public String getSecondaryContactFirstName() {
		return secondaryContactFirstName;
	}

	public void setSecondaryContactFirstName(String secondaryContactFirstName) {
		this.secondaryContactFirstName = secondaryContactFirstName;
	}

	public String getSecondaryContactLastName() {
		return secondaryContactLastName;
	}

	public void setSecondaryContactLastName(String secondaryContactLastName) {
		this.secondaryContactLastName = secondaryContactLastName;
	}

	public String getSecondaryContactPhoneNumber() {
		return secondaryContactPhoneNumber;
	}

	public void setSecondaryContactPhoneNumber(String secondaryContactPhoneNumber) {
		this.secondaryContactPhoneNumber = secondaryContactPhoneNumber;
	}

	public String getSecondaryContactMobilePhoneNumber() {
		return secondaryContactMobilePhoneNumber;
	}

	public void setSecondaryContactMobilePhoneNumber(String secondaryContactMobilePhoneNumber) {
		this.secondaryContactMobilePhoneNumber = secondaryContactMobilePhoneNumber;
	}

	public String getSecondaryContactEmail() {
		return secondaryContactEmail;
	}

	public void setSecondaryContactEmail(String secondaryContactEmail) {
		this.secondaryContactEmail = secondaryContactEmail;
	}

	public String getSecondaryContactDob() {
		return secondaryContactDob;
	}

	public void setSecondaryContactDob(String secondaryContactDob) {
		this.secondaryContactDob = secondaryContactDob;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
