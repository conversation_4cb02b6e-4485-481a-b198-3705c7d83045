{"feature": "Create User", "testData": [{"scenario": "Create user in Csmart Application", "data": [{"userLoginRole": {"userName": "test", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "password": "Password$111", "confirmPassword": "Password$111", "email": "<EMAIL>", "businessRegion": "All", "currencyLimit": "", "ipAddress": "", "voiceLimit": "", "dataAdjustmentLimit": "", "smsAdjustmentLimit": "", "promoAdjustmentLimit": "", "tenancyName": "", "verificationToken": ""}, "currencyConfiguration": {"currency": "USA, Dollars", "decimalSeperator": ".", "symbolPlacement": "$1.0", "digitGroupingPattern": "12,34,56,789", "digitGroupingSeperator": ",", "truncateTrailingZeros": false}, "moreInformationDetails": {"title": "Partner", "department": "Mr", "officePhone": "AutoSecTest", "secondaryEmail": "", "mobile": "SecondaryTester", "homePhone": "0444094584", "fax": "0444094584", "otherEmail": "<EMAIL>", "internalMailComposer": false, "crmPhoneExtension": "", "leftPanelHide": false, "language": "US English", "defaultRecordView": "Summary"}, "addressDetails": {"streetAddress": "Not Required", "city": 1, "state": "VIC", "country": "Australia", "postcode": 3000}}, {"userLoginRole": {"userName": "Vodafone", "firstName": "AutoTest", "lastName": "Tester", "password": "Password$111", "role": "SYMBIO- Finance Admin", "confirmPassword": "Password$111", "email": "<EMAIL>", "businessRegion": "Mumbai", "currencyLimit": "", "ipAddress": "", "voiceLimit": "", "dataAdjustmentLimit": "", "smsAdjustmentLimit": "", "promoAdjustmentLimit": "", "tenancyName": "", "verificationToken": ""}, "currencyConfiguration": {"currency": "USA, dollars", "decimalSeperator": ".", "symbolPlacement": "$1.0", "digitGroupingPattern": "12,34,56,789", "digitGroupingSeperator": ".", "truncateTrailingZeros": false}, "moreInformationDetails": {"title": "Partner", "department": "Mr", "officePhone": "AutoSecTest", "mobile": "SecondaryTester", "secondaryEmail": "", "homePhone": "0444094584", "fax": "0444094584", "otherEmail": "<EMAIL>", "internalMailComposer": false, "crmPhoneExtension": "", "leftPanelHide": false, "language": "US English", "defaultRecordView": "Summary"}, "addressDetails": {"streetAddress": "Not Required", "city": 1, "state": "VIC", "country": "Australia", "postcode": 3000}}, {"userLoginRole": {"userName": "Vodafone", "firstName": "AutoTest", "lastName": "Tester", "password": "Password$111", "role": "SYMBIO- Finance Admin", "confirmPassword": "Password$111", "email": "<EMAIL>", "businessRegion": "Mumbai", "currencyLimit": "", "ipAddress": "", "voiceLimit": "", "dataAdjustmentLimit": "", "smsAdjustmentLimit": "", "promoAdjustmentLimit": "", "tenancyName": "", "verificationToken": ""}, "currencyConfiguration": {"currency": "USA, dollars", "decimalSeperator": ".", "symbolPlacement": "$1.0", "digitGroupingPattern": "12,34,56,789", "digitGroupingSeperator": ".", "truncateTrailingZeros": false}, "moreInformationDetails": {"title": "Partner", "department": "Mr", "officePhone": "AutoSecTest", "mobile": "SecondaryTester", "secondaryEmail": "", "homePhone": "0444094584", "fax": "0444094584", "otherEmail": "<EMAIL>", "internalMailComposer": false, "crmPhoneExtension": "", "leftPanelHide": false, "language": "US English", "defaultRecordView": "Summary"}, "addressDetails": {"streetAddress": "Not Required", "city": 1, "state": "VIC", "country": "Australia", "postcode": 3000}}, {"userLoginRole": {"userName": "Vodafone", "firstName": "AutoTest", "lastName": "Tester", "password": "Password$111", "role": "SYMBIO- Finance Admin", "confirmPassword": "Password$111", "email": "<EMAIL>", "businessRegion": "Mumbai", "currencyLimit": "", "ipAddress": "", "voiceLimit": "", "dataAdjustmentLimit": "", "smsAdjustmentLimit": "", "promoAdjustmentLimit": "", "tenancyName": "", "verificationToken": ""}, "currencyConfiguration": {"currency": "USA, dollars", "decimalSeperator": ".", "symbolPlacement": "$1.0", "digitGroupingPattern": "12,34,56,789", "digitGroupingSeperator": ".", "truncateTrailingZeros": false}, "moreInformationDetails": {"title": "Partner", "department": "Mr", "officePhone": "AutoSecTest", "mobile": "SecondaryTester", "secondaryEmail": "", "homePhone": "0444094584", "fax": "0444094584", "otherEmail": "<EMAIL>", "internalMailComposer": false, "crmPhoneExtension": "", "leftPanelHide": false, "language": "US English", "defaultRecordView": "Summary"}, "addressDetails": {"streetAddress": "Not Required", "city": 1, "state": "VIC", "country": "Australia", "postcode": 3000}}]}]}