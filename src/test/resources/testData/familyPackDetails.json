{"feature": "Activation Feature", "testData": [{"scenario": "Family pack with New activation for Provider", "data": [{"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111111", "confirmPassword": "Password$111111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "20-05-1991"}, "enterSecondaryContactDetails": false, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}]}, {"scenario": "Family pack with Port activation for Provider", "data": [{"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456", "existingMobileNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "11-08-1991"}, "enterSecondaryContactDetails": true, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456", "existingMobileNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "11-08-1991"}, "enterSecondaryContactDetails": true, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "11-08-1991"}, "enterSecondaryContactDetails": true, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "11-08-1991"}, "enterSecondaryContactDetails": true, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "11-08-1991"}, "enterSecondaryContactDetails": true, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "11-08-1991"}, "enterSecondaryContactDetails": true, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "11-08-1991"}, "enterSecondaryContactDetails": true, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "11-08-1991"}, "enterSecondaryContactDetails": true, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}]}, {"scenario": "Family pack with Correct Payment Details", "data": [{"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111111", "confirmPassword": "Password$111111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "20-05-1991"}, "enterSecondaryContactDetails": false, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00) - (A$ 125.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "fpProviderNickname": "TestProvider", "fpConsumerNickname": "TestConsumer", "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111111", "confirmPassword": "Password$111111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "20-05-1991"}, "enterSecondaryContactDetails": false, "secondaryContactDetails": {"secondaryContactRelationship": "Partner", "secondaryContactTitle": "Mr", "secondaryContactFirstName": "AutoSecTest", "secondaryContactLastName": "SecondaryTester", "secondaryContactPhoneNumber": "**********", "secondaryContactMobilePhoneNumber": "**********", "secondaryContactEmail": "<EMAIL>", "secondaryContactDob": "11-08-1991"}, "addressDetails": {"unitType": "Not Required", "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with Value or Data Pack", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$55 5G Family Plan - 2 Services - (A$ 55.00) - (A$ 125.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}]}]}