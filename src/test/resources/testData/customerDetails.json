{"feature": "Activation Feature", "testData": [{"scenario": "New User Activate a user with a new/port number", "data": [{"id": 1, "userName": "**********", "password": "Password$2", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456", "existingMobileNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "20-05-1991"}, "enterAuthorisedContactDetails": true, "authorisedContactDetails": {"authorisedContactRelationship": "Parent", "authorisedContactTitle": "Mr", "authorisedContactFirstName": "AutoSecTest", "authorisedContactLastName": "authorisedTester", "authorisedContactPhoneNumber": "**********", "authorisedContactMobilePhoneNumber": "**********", "authorisedContactEmail": "<EMAIL>", "authorisedContactDob": "11-08-1991"}, "addressDetails": {"address": "Wales Avenue", "unitType": "Not Required", "unitNumber": 1, "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000, "suburb": "MELBOURNE"}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "Australia"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "VIC20288", "driversLicenceCardNumber": "VIC20288"}, "rechargePlanDetails": "Payg15", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$25 Mobile Plan - (A$ 25.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "userName": "**********", "password": "Password$2", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456", "existingMobileNumber": "**********"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "20-05-1991"}, "enterAuthorisedContactDetails": true, "authorisedContactDetails": {"authorisedContactRelationship": "Parent", "authorisedContactTitle": "Mr", "authorisedContactFirstName": "AutoSecTest", "authorisedContactLastName": "authorisedTester", "authorisedContactPhoneNumber": "**********", "authorisedContactMobilePhoneNumber": "**********", "authorisedContactEmail": "<EMAIL>", "authorisedContactDob": "11-08-1991"}, "addressDetails": {"address": "Wales Avenue", "unitType": "Not Required", "unitNumber": 1, "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000, "suburb": "MELBOURNE"}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargePlanDetails": "Payg15", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$25 Mobile Plan - (A$ 25.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}]}, {"scenario": "Activate a existing user with new/port number", "data": [{"id": 1, "userName": "**********", "password": "Password$2", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456", "existingMobileNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "11-08-1991"}, "enterAuthorisedContactDetails": false, "authorisedContactDetails": {"authorisedContactRelationship": "Parent", "authorisedContactTitle": "Mr", "authorisedContactFirstName": "AutoSecTest", "authorisedContactLastName": "authorisedTester", "authorisedContactPhoneNumber": "**********", "authorisedContactMobilePhoneNumber": "**********", "authorisedContactEmail": "<EMAIL>", "authorisedContactDob": "11-08-1991"}, "addressDetails": {"address": "Wales Avenue", "unitType": "Not Required", "unitNumber": 1, "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000, "suburb": "MELBOURNE"}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargeOption": "Recharge with PAYG Credit", "rechargePlanDetails": "Payg30", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$249 1 Year Super Pack - (A$ 249.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "userName": "**********", "password": "Password$2", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "20-05-1991"}, "enterAuthorisedContactDetails": true, "authorisedContactDetails": {"authorisedContactRelationship": "Parent", "authorisedContactTitle": "Mr", "authorisedContactFirstName": "AutoSecTest", "authorisedContactLastName": "authorisedTester", "authorisedContactPhoneNumber": "**********", "authorisedContactMobilePhoneNumber": "**********", "authorisedContactEmail": "<EMAIL>", "authorisedContactDob": "11-08-1991"}, "addressDetails": {"address": "Wales Avenue", "unitType": "Not Required", "unitNumber": 1, "streetNumber": 1, "streetName": "Queen", "streetType": "Street", "state": "VIC", "postcode": 3000, "suburb": "MELBOURNE"}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargePlanDetails": "Payg35", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$25 Mobile Plan - (A$ 25.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "userName": "**********", "password": "Password$2", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456", "existingMobileNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "20-05-1991"}, "enterAuthorisedContactDetails": true, "authorisedContactDetails": {"authorisedContactRelationship": "Parent", "authorisedContactTitle": "Mr", "authorisedContactFirstName": "AutoSecTest", "authorisedContactLastName": "authorisedTester", "authorisedContactPhoneNumber": "**********", "authorisedContactMobilePhoneNumber": "**********", "authorisedContactEmail": "<EMAIL>", "authorisedContactDob": "11-08-1991"}, "addressDetails": {"address": "Wales Avenue", "unitType": "Not Required", "unitNumber": 1, "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000, "suburb": "MELBOURNE"}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "India"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "***********"}, "rechargePlanDetails": "Payg15", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$249 1 Year Super Pack", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}]}, {"scenario": "Customer with valid Payment Details", "data": [{"id": 1, "userName": "**********", "password": "Password$2", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456", "existingMobileNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "20-05-1991"}, "enterAuthorisedContactDetails": true, "authorisedContactDetails": {"authorisedContactRelationship": "Parent", "authorisedContactTitle": "Mr", "authorisedContactFirstName": "AutoSecTest", "authorisedContactLastName": "authorisedTester", "authorisedContactPhoneNumber": "**********", "authorisedContactMobilePhoneNumber": "**********", "authorisedContactEmail": "<EMAIL>", "authorisedContactDob": "11-08-1991"}, "addressDetails": {"address": "Wales Avenue", "state": "NSW", "postcode": 2747, "suburb": "JORDAN SPRINGS"}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "Australia"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "VIC20288", "driversLicenceCardNumber": "VIC20288"}, "rechargePlanDetails": "Payg15", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$25 Mobile Plan - (A$ 25.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}, {"id": 1, "userName": "**********", "password": "Password$2", "importDetails": {"existingProvider": "Vodafone", "existingAccountNumber": "123456", "existingMobileNumber": "123456"}, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "contactNumber": "**********", "email": "<EMAIL>", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "pinCode": "1234", "confirmPinCode": "1234", "dateOfBirth": "20-05-1991"}, "enterAuthorisedContactDetails": true, "authorisedContactDetails": {"authorisedContactRelationship": "Parent", "authorisedContactTitle": "Mr", "authorisedContactFirstName": "AutoSecTest", "authorisedContactLastName": "authorisedTester", "authorisedContactPhoneNumber": "**********", "authorisedContactMobilePhoneNumber": "**********", "authorisedContactEmail": "<EMAIL>", "authorisedContactDob": "11-08-1991"}, "addressDetails": {"address": "Wales Avenue", "unitType": "Not Required", "unitNumber": 1, "streetNumber": 1, "streetName": "Queen", "streetType": "ST", "state": "VIC", "postcode": 3000, "suburb": "MELBOURNE"}, "idType": "Passport", "passportDetails": {"passportName": "Tester Test", "passportNumber": "********", "nationality": "Australia"}, "medicareDetails": {"medicareNumber": "1234567899", "individualNameNumber": "1", "middleNameInitial": "J", "colorOfCard": "Green", "cardExpiryMonth": "05", "cardExpiryYear": "2020"}, "licenceDetails": {"driversLicenceState": "VIC", "driversLicenceNumber": "VIC20288", "driversLicenceCardNumber": "VIC20288"}, "rechargePlanDetails": "Payg15", "rechargeVoucherCodeDetails": "abcdef", "rechargePackDetails": "$25 Mobile Plan - (A$ 25.00)", "paymentDetails": {"nameOnCard": "Auto Tester", "cardNumber": "****************", "cardCVV": "567", "cardExpiry": "03/25", "paypalEmail": "<EMAIL>", "paypalPassword": "1*Zo+1Wd"}}]}, {"scenario": "Update Existing Customer Details", "data": [{"id": 1, "primaryContactDetails": {"title": "Ms", "firstName": "AutoTest", "middleName": "Test", "lastName": "Tester", "confirmEmail": "<EMAIL>", "password": "Password$111", "confirmPassword": "Password$111", "dateOfBirth": "20-05-1991", "contactNumber": "**********", "email": "<EMAIL>", "pinCode": "4321", "confirmPinCode": "4321"}, "addressDetails": {"address": "Waley Avenue", "unitType": "U", "unitNumber": 5, "streetNumber": 1, "streetName": "Exhibition", "suburb": "EAST MELBOURNE", "streetType": "ST", "state": "VIC", "postcode": 3002}}]}]}