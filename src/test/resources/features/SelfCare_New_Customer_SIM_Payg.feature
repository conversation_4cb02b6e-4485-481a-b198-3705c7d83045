@selfcare
Feature: Regression Testing For New Customer in SelfCare

  	@skipcheck @activate @sim @payg
   	Scenario Outline: Activate New number using Credit Card
   	#Given Agent logs into Csmart Application
   	#When User navigates to 'Inventory Catalogue' 'Inventory' page in Csmart
    #Then User selects activation code for 'Physical SIM' with 'Available' status
   	Given Customer navigates to Self Care Website
   	And 'New' '<Customer>' activates a new 'Physical SIM'
   	Then Customer performs number transfer verification for Port Orders
   	Then Customer enters activation code from starter pack
   	And Customer selects '<ActivationType>' number
   	And Enters customer details in customer details page
   	Then Enters address details in address details page
   	Then Customer selects 'Select a Plan' 'Mobile Plan' with '$44 Mobile Plan'
   	And Set Plan Auto Recharge status to 'ON'
   	#Then Customer enters 'Paypal' payment details
   	#Then User performs ID Check using 'Drivers Licence' when needed
   	#Then Enter 'Valid' payment details using 'Credit Card' in Verification Payment Page when needed
   	Then User submits activation order upon reviewing customer details
   	And Activation order number and account number are displayed upon submission
   	And Write 'Account' details to file
		Then Agent logs into Csmart Application
		When User navigates to 'Customers Management' 'Sales Order' page in Csmart
		And Verify that order number is in 'Completed' status in Csmart
		Then '<Customer>' logs into Self Care Website
 		And Customer is on SelfCare homepage
 		Then User navigates to 'Account' page in SelfCare
 		And User navigates to 'Personal information' details page
 		And User verifies 'Personal information' details
 		And User verifies 'Address' details
 		And User navigates to 'Payment' details page
 		And User verifies 'Payment information' details 
 		Then User navigates to 'Dashboard' page in SelfCare
 		And Service Number is 'Active' in Selfcare Application
		And User navigates to 'Orders' page in SelfCare
 		#And Verify Order Number is 'Completed'
 		And User navigates to 'Invoice' details page
 		#And Invoice is generated for the transaction
 		#And Verify statement PDF details and charges are correct
   	 		
   	 	Examples:
      | Customer |  ActivationType |
      | 1    |			New 					 |
     #| 2    |			Port 					 |
