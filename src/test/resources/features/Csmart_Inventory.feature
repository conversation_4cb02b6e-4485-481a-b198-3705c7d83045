@regression @inventory
Feature: Regression Testing For Csmart Application

		Background:
		Given Agent logs into Csmart Application
		Then User is on Csmart Applicaton homepage

    @uploadsim
    Scenario: Upload SIM in Inventory Catalogue
    When User navigates to 'Support' 'Platform Settings' page in Csmart
		Then Validate that CSV Header is set to 'Yes'
    When User navigates to 'Inventory Catalogue' 'Inventory' page in Csmart
    And Creates upload CSV file with 2 'Physical SIM'
    And Checks for duplicate entries using 'Sim Number'
    When User uploads file in Import Inventory
    And User Enters import details
    And Use 'Skip' as duplicate handling method
    Then Validate that 'Physical SIM' are successfully imported with correct status
    
    @uploadmsn	
    Scenario: Upload MSN in Inventory Catalogue
    When User navigates to 'Support' 'Platform Settings' page in Csmart
		Then Validate that CSV Header is set to 'Yes'
    When User navigates to 'Inventory Catalogue' 'Inventory' page in Csmart
    And Creates upload CSV file with 2 'MSN'
    And Checks for duplicate entries using 'MSN'
    When User uploads file in Import Inventory
    And User Enters import details
    And Use 'Overwrite' as duplicate handling method
    Then Validate that 'MSN' are successfully imported with correct status
    
    @uploadesim
    Scenario: Upload eSIM in Inventory Catalogue
    When User navigates to 'Support' 'Platform Settings' page in Csmart
		Then Validate that CSV Header is set to 'Yes'
    When User navigates to 'Inventory Catalogue' 'Inventory' page in Csmart
    And Creates upload CSV file with 1 'eSIM'
    And Checks for duplicate entries using 'ICCID'
    When User uploads file in Import Inventory
    And User Enters import details
    And Use 'Skip' as duplicate handling method
    Then Validate that 'eSIM' are successfully imported with correct status
    
    @createvoucherpin
    Scenario: User creates voucher pin
   	When User navigates to 'Support' 'Tickets' page in Csmart
    Then User creates ticket for 5 Voucher Code PIN with 'Available' status
    And User Bypass Approval is set to 'No'
    When User navigates to 'Inventory Catalogue' 'Recharge Voucher' page in Csmart
    And User 'Approve' the voucher request
		And User generates voucher in Inventory Catalogue
		Then Validate that voucher are added to Inventory Catalogue in 'Available' status
    
    @voidsim @bulkoperation @upload
    Scenario: User voids multiple SIM via Bulk Operation
   	When User navigates to 'Inventory Catalogue' 'Inventory' page in Csmart
    And Selects 2 'Physical SIM' with 'Available' status for 'Void Sim' Operation
    When User navigates to 'Settings' 'CRM Settings' page in Csmart
    Then User Navigates to 'Bulk Operations' option under 'Tools'
    And User performs Bulk Operation

    @voidvoucher @bulkoperation @upload
    Scenario: User voids multiple Voucher via Bulk Operation
   	When User navigates to 'Inventory Catalogue' 'Recharge Voucher' page in Csmart
   	And Selects 5 'Voucher' with 'Available' status for 'Void Voucher' Operation
    When User navigates to 'Settings' 'CRM Settings' page in Csmart
    Then User Navigates to 'Bulk Operations' option under 'Tools'
    And User performs Bulk Operation
    
    @generatevoucher @bulkoperation @upload
    Scenario: User Generates voucher via Bulk Operation
   	When User navigates to 'Inventory Catalogue' 'Recharge Voucher' page in Csmart
    And Selects 5 'Voucher' with 'Generated' status for 'Generate Voucher' Operation
    When User navigates to 'Settings' 'CRM Settings' page in Csmart
    Then User Navigates to 'Bulk Operations' option under 'Tools'
    And User performs Bulk Operation
 
    @smoke @voidsim @single
    Scenario: User voids single SIM in Inventory Page
    When User navigates to 'Inventory Catalogue' 'Inventory' page in Csmart
    And User changes status of 1 'Physical SIM' in Inventory from 'Available' to 'Void'
    Then Validate that 'SIM Number' status is changed to 'Void'
    
    @voidsim @multiple
    Scenario: User voids multiple SIM in Inventory Page
    When User navigates to 'Inventory Catalogue' 'Inventory' page in Csmart
    And User changes status of 2 'Physical SIM' in Inventory from 'Available' to 'Void'
    Then Validate that 'SIM Number' status is changed to 'Void'
    
    @smoke @voidvoucher @single
    Scenario: User voids single Voucher in Inventory Page
    When User navigates to 'Inventory Catalogue' 'Recharge Voucher' page in Csmart
    And User changes status of 1 'Voucher' in Inventory from 'Available' to 'Void'
    Then Validate that 'Voucher Control ID' status is changed to 'Void'

		@voidvoucher @multiple
    Scenario: User voids multiple Voucher in Inventory Page
    When User navigates to 'Inventory Catalogue' 'Recharge Voucher' page in Csmart
    And User changes status of 3 'Voucher' in Inventory from 'Available' to 'Void'
    Then Validate that 'Voucher Control ID' status is changed to 'Void'
		
		@messageoftheday
		Scenario: User creates message of the day in Csmart Application
   	When User navigates to 'Support' 'Message Of The Day' page in Csmart
   	And User creates MOTD with 'Information' Message Type and 'Active' status
    When User navigates to 'Dashboard' page in Csmart
   	And Validate that MOTD is displayed in Csmart Dashboard
   	
   	@voidsim @bulkoperation @validation
   	Scenario: Validates that SIM is changed to VOID after Bulk Operation
   	When User navigates to 'Settings' 'CRM Settings' page in Csmart
    Then User Navigates to 'Bulk Operations' option under 'Tools'
    And Validate 'Void SIM' Bulk Operation status is 'Completed'
   	When User navigates to 'Inventory Catalogue' 'Inventory' page in Csmart
    Then Validate that 'SIM Number' status is changed to 'Void'
		
		@generatevoucher @bulkoperation @validation
    Scenario: Validates that Voucher is changed to AVAILABLE after Bulk Operation
    When User navigates to 'Settings' 'CRM Settings' page in Csmart
    Then User Navigates to 'Bulk Operations' option under 'Tools'
    And Validate 'Generate Voucher' Bulk Operation status is 'Completed'
    When User navigates to 'Inventory Catalogue' 'Recharge Voucher' page in Csmart
    Then Validate that 'Voucher Control ID' status is changed to 'Available'
    
    @voidvoucher @bulkoperation @validation
    Scenario: Validates that Voucher is changed to VOID after Bulk Operation
    When User navigates to 'Settings' 'CRM Settings' page in Csmart
    Then User Navigates to 'Bulk Operations' option under 'Tools'
    And Validate 'Void Voucher' Bulk Operation status is 'Completed'
    When User navigates to 'Inventory Catalogue' 'Recharge Voucher' page in Csmart
    Then Validate that 'Voucher Control ID' status is changed to 'Void'
