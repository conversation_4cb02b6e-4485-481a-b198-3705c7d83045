@regression @cmart
Feature: User Management Testing in Csmart Application
     
		Background:
		Given Agent logs into Csmart Application
		Then User is on Csmart Applicaton homepage

    @account
    Scenario: Create customer account in Csmart
    When User navigates to 'Customers Management' 'Accounts' page in Csmart
    Then Agent creates new customer account
    And Enters customer details
    And User navigates to 'Customers Management' 'Accounts' page in Csmart
    Then Customer account is 'Prospect'
    
    @activate 
    Scenario Outline: Activate New MSN with Physical SIM in Csmart
    And User navigates to 'Customers Management' 'Accounts' page in Csmart
    Then Customer account is 'Active' 
    
     	Examples:
      | Customer |  ActivationType |
      | 1    |			New 					 |
     #| 2    |			Port 					 |
    