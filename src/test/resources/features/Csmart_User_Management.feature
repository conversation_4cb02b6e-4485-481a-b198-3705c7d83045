@usermanagement
Feature: User Management Testing in Csmart Application
     
    @usermanagement
    Scenario Outline: Checks Privileges for Specific Role
    Given User '<User>' with role '<Role>' logs into Csmart Application
    Then User is on Csmart Applicaton homepage
   	Then User should have privileges based on the role
   	Then Validates the users permission based on the privilege
   	
   	 	Examples:
      | User 			|  		Role 		 			 						|
     	| 1    			|			Super Admin 		  				|
     #| 2    			|			Platform Head 						|
   	 #| 3    			|			Compliance Head 					|
  	 #| 4    			|			Operations Head 					|	
  	 #| 5    			|			Symbio L3 Agent 					|
  	 #| 1    			|			Super Admin 		  				|
     #| 2    			|			Platform Head 						|
   	 #| 3    			|			Compliance Head 					|
  	 #| 4    			|			Product Head		 					|
  	 #| 5    			|			Finance Head		 					|
  	 #| 6    			|			Planning Head							|
  	 #| 7    			|			Content Manager		 				|
  	 #| 8    			|			Product Manager		 				|
  	 #| 9    			|			Finance Manager		 				|
  	 #| 10    		|			Sales Manager		 					|
  	 #| 11    		|			Logistics Manager		 			|
  	 #| 12    		|			AU L3 Agent		 						|
  	 #| 13    		|			L2 Agent	 								|
  	 #| 14    		|			L1 Agent	 								|