csmart.url= https://skyhigh-staging.symbio.global
csmart.enableradmin.username= symbio
csmart.enableradmin.password= Admin@123
csmart.spadmin.username= medion
csmart.spadmin.password= Admin@123
csmart.sp.username= aldimobile
csmart.sp.password= Admin@123
customer.details = testData/customerDetails.json
family.details = testData/familyPackDetails.json
update.details = testData/updateDetails.json
aldiMobile.url = http://10.1.18.10:4000/
sim.upload = /skyhigh/skyhigh.sim.upload.csv
msn.upload = /skyhigh/skyhigh.msn.upload.csv
esim.upload = /skyhigh/skyhigh.esim.upload.csv
void.voucher.upload = /skyhigh/skyhigh.void.voucher.upload.csv
void.sim.upload = /skyhigh/skyhigh.void.sim.upload.csv
generate.voucher.upload = /skyhigh/skyhigh.generate.voucher.upload.csv
csmart.superadmin.username = SymbioAdmin
csmart.superadmin.password = Admin@123
csmart.platform.username = SymPlatHead
csmart.platform.password = Admin@123
csmart.compliance.username = SymCompHead
csmart.compliance.password = Admin@123
csmart.operations.username = SymOpHead
csmart.operations.password = Admin@123
csmart.agent.username = SymL3Agent
csmart.agent.password = Password@123
privilege.path = _privilege.json