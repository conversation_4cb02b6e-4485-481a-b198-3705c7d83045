csmart.url= https://skyhigh-staging.symbio.global
csmart.sp.username= aldiauto
csmart.sp.password= Admin@123
customer.details = testData/customerDetails.json
family.details = testData/familyPackDetails.json
update.details = testData/updateDetails.json
selfcare.url = https://my-staging.aldimobile.com.au/
sim.upload = /skyhigh/skyhigh.sim.upload.csv
msn.upload = /skyhigh/skyhigh.msn.upload.csv
esim.upload = /skyhigh/skyhigh.esim.upload.csv
void.voucher.upload = /skyhigh/skyhigh.void.voucher.upload.csv
void.sim.upload = /skyhigh/skyhigh.void.sim.upload.csv
generate.voucher.upload = /skyhigh/skyhigh.generate.voucher.upload.csv
csmart.superadmin.username = supadmin
csmart.superadmin.password = Admin@1234
csmart.platform.username = platfhead
csmart.platform.password = Admin@1234
csmart.compliance.username = cscomphead
csmart.compliance.password = Admin@1234
csmart.operations.username = SymOpHead
csmart.operations.password = Admin@1234
csmart.producthead.username = prducthead
csmart.producthead.password = Admin@1234
csmart.financehead.username = financehead
csmart.financehead.password = Admin@1234
csmart.planning.username = parhead
csmart.planning.password = Admin@1234
csmart.content.username = tacmgr
csmart.content.password = Admin@1234
csmart.productmanager.username = prdmgr
csmart.productmanager.password = Admin@1234
csmart.financemanager.username = financemgr
csmart.financemanager.password = Admin@1234
csmart.sales.username = salesmgr
csmart.sales.password = Admin@1234
csmart.logistic.username = logisticmgr
csmart.logistic.password = Admin@1234
csmart.l3.username = aldilvl3
csmart.l3.password = Password@123
csmart.l2.username = aldilvl2
csmart.l2.password = Password@123
csmart.l1.username = aldilvl1
csmart.l1.password = Password@123
privilege.path = _privilege.json
testData.file = /skyhigh/skyhigh.qa.testdata.csv
invoice.file = /skyhigh/skyhigh.qa.invoice_