package runner;

import org.junit.runner.RunWith;

import io.cucumber.junit.Cucumber;
import io.cucumber.junit.CucumberOptions;


@RunWith(Cucumber.class)
@CucumberOptions(
    features = "src/test/resources/features",
    glue = {"com.stepDefinition"},
    plugin = {"html:target/cucumber-html-report.html", "json:target/cucumber.json", "junit:target/surefire-reports/TEST-cucumber.xml"},
    monochrome = true
)

public class TestRunner {

	/*
	 * @BeforeClass public static void createAndGetCucumberOption() throws
	 * IOException { Properties prop; FileInputStream fis = new FileInputStream(
	 * System.getProperty("user.home").replace("\\", "/") + "/medion/medion.
	 * dataconfig.properties"); prop = new Properties(); prop.load(fis);
	 * 
	 * String tags = prop.getProperty("cucumber.options");
	 * System.setProperty("cucumber.options", tags); System.out.print("TEST" +
	 * System.getProperty("cucumber.options")); }
	 * 
	 */
}
 