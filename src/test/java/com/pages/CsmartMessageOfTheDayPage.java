package com.pages;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import org.junit.Assert;

import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.elements.TextElement;
import com.elements.TextfieldElement;
import com.enums.MessageType;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.utils.SkyHighUtil;


public class CsmartMessageOfTheDayPage extends SkyHighPage {
	

    public CsmartMessageOfTheDayPage(Page driver) {
    	super(driver);
    }
	
	private final static String TENANT_SUCCESS_MESSAGE= "Tenants Associated Successfully.";
	private final static String MESSAGE_CONTENT= "Purchase a SUM from our online store today - no ALDI Mobile account is needed for gifts, recipients can activate and set up their own account when they activate before 31 January 2025. To claim the offer or learn more, visit <html><u>aldimobile.com.au/plans</u></html>.";
	String backgroundColor;
	String messageType;
	String messageDisplay;
	private Locator addRecord = driver.locator("//*[@id=\"MessageOfTheDay_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator startDateField = driver.locator("//*[@id=\"MessageOfTheDay_editView_fieldName_display_start_time\"]");
	private Locator endDateField = driver.locator("//*[@id=\"MessageOfTheDay_editView_fieldName_display_end_time\"]");
	private Locator crmDisplayCheckBox = driver.locator("//*[@id=\"MessageOfTheDay_editView_fieldName_display_in_crm\"]");
	private Locator selfCareisplayCheckBox = driver.locator("//*[@id=\"MessageOfTheDay_editView_fieldName_display_in_selfcare\"]");
	private Locator statusTypeDropdown = driver.locator("#select2-chosen-4");
	private Locator statusTypeField = driver.locator("#s2id_autogen4_search");
	private Locator messageTypeDropdown = driver.locator("#select2-chosen-6");
	private Locator messageTypeField = driver.locator("#s2id_autogen6_search");
	private Locator tenantSearchIcon = driver.locator("//*[@id=\"MessageOfTheDay_editView_fieldName_tenancy_id_select\"]");
	private Locator tenantSearchField = driver.locator("//*[@name='rolename']");
	private Locator tenantSearchButton = driver.locator("//button[contains(.,'Search')]");
	private Locator tenantSearchResult = driver.locator("//*[@id=\"Tenants_popUpListView_row_1\"]/td[2]");
	private Locator tenantSuccessMessage = driver.locator(".col-xs-11 > div:nth-child(2)");
	private Locator messageField = driver.locator("iframe[title=\"Rich Text Editor\\, msgckeditor1\"]").contentFrame().locator("body");
    private Locator colorInput = driver.locator("input[type='color']");
    private Locator saveButon = driver.locator("//button[contains(.,'Save')]");
    private Locator motdDetails = driver.locator("//form[@id='detailView']/div");
    private Locator criticalIcon = driver.locator("//*[@id=\"messageIcon\"][@class='fa fa-times-circle']").last();
    private Locator informationIcon = driver.locator("//*[@id=\"messageIcon\"][@class='fa fa-info-circle']").last();
    private Locator disclaimerIcon = driver.locator("//*[@id=\"messageIcon\"][@class='fa fa-check-circle']").last();
    private Locator warningIcon = driver.locator("//*[@id=\"messageIcon\"][@class='fa fa-exclamation-triangle']").last();
    private Locator messageDisplayText = driver.locator("//*[@id=\"MessageOfTheDay_detailView_fieldValue_message\"]/span");
    private Locator messageDisplayColor = driver.locator("//*[@id=\"messageContainer\"]").last();
  
	public void enterMessageOfTheDayDetails(String messageType, String tenant, String motdStatus) {
		log.info("Enter Message of The Day Details");
		new ButtonElement(driver, addRecord).click();
		new ButtonElement(driver, startDateField).click();
		new TextfieldElement(driver, startDateField).setValue(SkyHighUtil.getFutureDateTime("dd-MM-yyyy hh:mm a", 1, "minute"));
		new ButtonElement(driver, endDateField).click();
		new TextfieldElement(driver, endDateField).setValue(SkyHighUtil.getFutureDateTime("dd-MM-yyyy hh:mm a", 1, "day"));
		new ButtonElement(driver, crmDisplayCheckBox).click();
		new ButtonElement(driver, selfCareisplayCheckBox).click();
		new ButtonElement(driver, tenantSearchIcon).click();
		new TextfieldElement(driver, tenantSearchField).setValue(tenant);
		new ButtonElement(driver, tenantSearchButton).click();
		driver.waitForTimeout(3000);
		new ButtonElement(driver, tenantSearchResult).click();
		driver.waitForTimeout(3000);
		assertThat("Tenant is not successfully associated", tenantSuccessMessage.innerText().trim(), equalTo(TENANT_SUCCESS_MESSAGE));
		new DropdownElement(driver, statusTypeDropdown).select2DropdownSelect(statusTypeField, motdStatus);
		new DropdownElement(driver, messageTypeDropdown).select2DropdownSelect(messageTypeField, messageType);
		this.messageType = messageType;
		backgroundColor = SkyHighUtil.getRandomHexColor();
		new TextfieldElement(driver, colorInput).setValueByColor(backgroundColor);
		new ButtonElement(driver, messageField).click();
		new TextfieldElement(driver, messageField).setValue(MESSAGE_CONTENT);
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		new ButtonElement(driver, saveButon).click();
		driver.waitForTimeout(3000);

	}

	public void validateDisplayCRM() {
		log.info("Validate that Message of The Day is displayed in CRM");
		driver.waitForTimeout(3000);
		Locator motdDisplay = driver.locator("//*[@id=\"messageContainer\"]/div/div/span[2][contains(.,'" + messageDisplay + "')]").last();
		assertThat("MOTD is not displayed in Dashboard", motdDisplay.isVisible(), equalTo(true));
		assertThat("Background color is not the same", messageDisplayColor.getAttribute("style").contains(backgroundColor));
		validateMessageTypeIcon();
	}

	private void validateMessageTypeIcon() {
		log.info("Validate Message Type Icon");
		switch (MessageType.fromDescription(this.messageType)) {

		case CRITICAL:
			new TextElement(driver, criticalIcon).checkVisibility();
			break;
		case INFORMATION:
			new TextElement(driver, informationIcon).checkVisibility();
			break;
		case DISCLAIMER:
			new TextElement(driver, disclaimerIcon).checkVisibility();
			break;
		case WARNING:
			new TextElement(driver, warningIcon).checkVisibility();
			break;
		default:
			Assert.fail("Invalid Message Type : " + messageType);
		}
	}

	public void validateMotdDetails() {
		log.info("Validate MOTD Details");
		assertThat("Message of the Day Details are not displayed", motdDetails.first().isVisible(), equalTo(true));
		messageDisplay = messageDisplayText.innerText();
		SkyHighUtil.delayRun(1, driver);
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}
}
