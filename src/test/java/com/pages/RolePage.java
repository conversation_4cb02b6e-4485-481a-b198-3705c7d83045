package com.pages;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import org.junit.Assert;

import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.enums.CsmartModule;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.PlaywrightException;
import com.role.Privileges;
import com.role.RoleDetails;
import com.utils.SkyHighUtil;

public class RolePage extends SkyHighPage {

	public RolePage(Page driver) {
		super(driver);
	}

	private Locator accountAddButton = driver.locator("//*[@id=\"Accounts_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator accountDetails = driver.locator("//*[@id=\"Accounts_listView_row_1\"]/td[2]/span/span/a");
	private Locator accountStatusDropdown = driver.locator("#s2id_autogen5");
	private Locator accountStatusField = driver.locator("#s2id_autogen5 .select2-input");
	private Locator accountEditButton = driver.locator("//*[@class='editBlockAction LBL_ACCOUNT_INFORMATION fa fa-pencil']");
	private Locator invoiceAddButton = driver.locator("//*[@id=\"Invoice_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator accountNumberField = driver.locator("//*[@name='account_no']");
	private Locator invoiceDetails = driver.locator("//*[@id=\"Invoice_listView_row_1\"]/td[2]/span/span/a");
	private Locator invoiceEditButton = driver.locator("//*[@id=\"Invoice_detailView_basicAction_LBL_EDIT\"]");
	private Locator switchEyeIcon = driver.locator("//*[@class='fal fa-eye']");
	private Locator detailsTab = driver.locator("//strong[contains(.,'Details')]");
	private Locator contactsTab = driver.locator("//span[@class='tab-icon']//i[@title='Authorised Contacts']");
	private Locator contactDetails = driver.locator("//*[@id=\"Contacts_listView_row_1\"]/td[2]/span/span/a");
	private Locator contactEditButton = driver.locator("//*[@id=\"Contacts_detailView_basicAction_LBL_EDIT\"]");
	private Locator motdAddButton = driver.locator("//*[@id=\"MessageOfTheDay_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator motdDetails = driver.locator("//*[@id=\"MessageOfTheDay_listView_row_1\"]/td[2]/span/span/a");
	private Locator motdEditButton = driver.locator("//*[@id=\"MessageOfTheDay_detailView_basicAction_LBL_EDIT\"]");
	private Locator ticketAddButton = driver.locator("//*[@id=\"HelpDesk_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator ticketDetails = driver.locator("//*[@id=\"HelpDesk_listView_row_1\"]/td[2]/span/span/a");
	private Locator ticketEditButton = driver.locator("//*[@id=\"HelpDesk_detailView_basicAction_LBL_EDIT\"]");
	private Locator ticketTitle = driver.locator("//*[@name='ticket_title']");
	private Locator inventoryAddButton = driver.locator("//*[@id=\"Inventory1_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator inventoryDetails = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/td[2]/span/span/a");
	private Locator inventoryEditButton = driver.locator("//*[@id=\"Inventory1_detailView_basicAction_LBL_EDIT\"]");
	private Locator inventoryStatusDropdown = driver.locator("#s2id_autogen1");
	private Locator inventoryStatusSearchField = driver.locator("#s2id_autogen1 .select2-input");
	private Locator inventoryNo = driver.locator("//*[@name='inventoryno']");
	private Locator rechargeStatusDropdown = driver.locator("#s2id_autogen3");
	private Locator rechargeSearchField = driver.locator("#s2id_autogen3 .select2-input");
	private Locator voucherNoField = driver.locator("//*[@name='voucherno']");
	private Locator moreButton = driver.locator("//*[@id=\"listview-actions\"]/div/div[1]/div/div/button");
	private Locator voidOptionButton = driver.locator("//*[@id=\"Inventory1_listView_advancedAction_Void_SIM\"] | //*[@id=\"RechargeVoucher_listView_advancedAction_Void_Voucher\"]");
	private Locator salesStatusDropdown = driver.locator("#s2id_autogen3");
	private Locator salesCategorySearch = driver.locator("#s2id_autogen3 .select2-input");
	private Locator salesDetails = driver.locator("//*[@id=\"SalesOrder_listView_row_1\"]/td[2]/span/span/a");
	private Locator salesEditButton = driver.locator("//*[@id=\"SalesOrder_detailView_basicAction_LBL_EDIT\"]");
	private Locator salesSubmitButton = driver.locator("//*[@id=\"SalesOrder_detailView_basicAction_Submit\"]");
	private Locator voucherDetails = driver.locator("//*[@id=\"RechargeVoucher_listView_row_1\"]/td[2]/span/span/a");
	private Locator salesOrderNumberField = driver.locator("//*[@name='salesorder_no']");
	private Locator voucherEditButton = driver.locator("//*[@id=\"RechargeVoucher_detailView_basicAction_LBL_EDIT\"]");
	private Locator generateButton = driver.locator("//*[@id=\"RechargeVoucher_listView_advancedAction_Generate_Vouchers\"]");
	private Locator activateVoucherButton = driver.locator("//*[@id=\"RechargeVoucher_listView_advancedAction_Activate_Voucher\"]");
	private Locator bulkStatusUpdateOption = driver.locator("//a[contains(text(),'Bulk Status Update')]");
	private Locator exportButton = driver.locator("//*[@id=\"RechargeVoucher_listView_advancedAction_Export_Voucher\"]");
	private Locator ticketStatusDropdown = driver.locator("#s2id_autogen1");
	private Locator ticketCategorySearch = driver.locator("#s2id_autogen1 .select2-input");
	private Locator searchButton = driver.locator("//*[@id=\"listview-table\"]/thead/tr[2]/th[1]/div/button");
	private Locator documentAddButton = driver.locator("//*[@id=\"appnav\"]/ul/li[1]/div/button");
	private Locator documentDetails = driver.locator("//*[@id=\"Documents_listView_row_1\"]/td[2]/span/span/a");
	private Locator documentEditButtom = driver.locator("//*[@id=\"Documents_detailView_basicAction_LBL_EDIT\"]");
	private Locator securityQuestionAddButton = driver.locator("//*[@id=\"SecurityQuestions_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator securityQuestionDetails = driver.locator("//*[@id=\"SecurityQuestions_listView_row_1\"]/td[2]/span/span/a");
	private Locator securityQuestionEditButton = driver.locator("//*[@id=\"SecurityQuestions_detailView_basicAction_LBL_EDIT\"]");
	private Locator templateAddButton = driver.locator("//*[@id=\"NotesTemplates_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator templateDetails = driver.locator("//*[@id=\"NotesTemplates_listView_row_1\"]/td[2]/span[1]/span/a");
	private Locator templateEditButton = driver.locator("//*[@id=\"NotesTemplates_detailView_basicAction_LBL_EDIT\"]");
	private Locator reportAddButton = driver.locator("//*[@id=\"Reports_listView_basicAction_Add\"]");
	private Locator reportDetails = driver.locator("//*[@id=\"Reports_listView_row_1\"]/td[3]/span/span");
	private Locator reportEditButton = driver.locator("//*[@title='Customize']");
	private Locator mnpAddButton = driver.locator("//*[@id=\"MNPRequests_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator addUserButton = driver.locator("//*[@id=\"Users_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator userDetails = driver.locator("//*[@id=\"Users_listView_row_1\"]/td[2]/span/span/div/div[2]/a");
	private Locator editUserButton = driver.locator("//*[@id=\"Users_detailView_basicAction_LBL_EDIT\"]");
	private Locator paymentsAddButton = driver.locator("//*[@id=\"Payments_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator paymentsStatusDropdown = driver.locator("#s2id_autogen1");
	private Locator paymentDateField = driver.locator("//*[@name='paymentdate']");
	private Locator paymentsStatusField = driver.locator("#s2id_autogen1 .select2-input");
	private Locator paymentElipsis = driver.locator("//*[@id=\"Payments_listView_row_1\"]/td[1]/div/span[2]/span/i");
	private Locator paymentEditOption = driver.locator("//*[@name='editlink']");
	private Locator campaignAddButton = driver.locator("//*[@id=\"Campaigns_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator campaignDetails = driver.locator("//*[@id=\"Campaigns_listView_row_1\"]/td[2]/span/span/a");
	private Locator campaignEditButton = driver.locator("//*[@id=\"Campaigns_detailView_basicAction_LBL_EDIT\"]");
	private Locator translationsAddButton = driver.locator("//*[@id=\"Translations_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator translationsDetails = driver.locator("//*[@id=\"Translations_listView_row_1\"]/td[2]/span/span/a");
	private Locator translationsEditButton = driver.locator("//*[@id=\"Translations_detailView_basicAction_LBL_EDIT\"]");
	private Locator faqAddButton = driver.locator("//*[@id=\"Faq_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator faqDetails = driver.locator("//*[@id=\"Faq_listView_row_1\"]/td[2]/span/span/a");
	private Locator faqEditButton = driver.locator("//*[@id=\"Faq_detailView_basicAction_LBL_EDIT\"]");
	private Locator productAddButton = driver.locator("//*[@id=\"Products_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator productDetails = driver.locator("//*[@id=\"Products_listView_row_1\"]/td[2]/span/span/a");
	private Locator productEditButton = driver.locator("//*[@id=\"Products_detailView_basicAction_LBL_EDIT\"]");
	private Locator packageAddButton = driver.locator("//*[@id=\"Packages_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator packageIcon = driver.locator("//*[@id=\"mCSB_10_container\"]/ul[3]/li/a/i");
	private Locator packageDetails = driver.locator("//*[@id=\"Packages_listView_row_1\"]/td[2]/span/span/a");
	private Locator packageEditButton = driver.locator("//*[@id=\"Packages_detailView_basicAction_LBL_EDIT\"]");
	private Locator policyAddButton = driver.locator("//*[@id=\"Planchangepolicy_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator policyIcon = driver.locator("//*[@id=\"mCSB_10_container\"]/ul[5]/li/a/i");
	private Locator policyDetails = driver.locator("//*[@id=\"Planchangepolicy_listView_row_1\"]/td[2]/span/span/a");
	private Locator policyEditButton = driver.locator("//*[@id=\"Planchangepolicy_detailView_basicAction_LBL_EDIT\"]");
	private Locator vendorIcon = driver.locator("//*[@id=\"mCSB_10_container\"]/ul[3]/li/a/i");
	private Locator vendorAddButton = driver.locator("//*[@id=\"Vendors_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator vendorDetails = driver.locator("//*[@id=\"Vendors_listView_row_1\"]/td[2]/span[1]/span/a");
	private Locator vendorEditButton = driver.locator("//*[@id=\"Vendors_detailView_basicAction_LBL_EDIT\"]");
	private Locator emailAddButton = driver.locator("//*[@id=\"EmailTemplates_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator emailDetails = driver.locator("//*[@id=\"EmailTemplates_listView_row_1\"]/td[2]/span[1]/span");
	private Locator emailEditButton = driver.locator("//*[@id=\"EmailTemplates_detailView_basicAction_LBL_EDIT\"]");
	private Locator smsButton = driver.locator("//*[@id=\"mCSB_10_container\"]/ul[5]/li/a/i");
	private Locator smsAddButton = driver.locator("//*[@id=\"SMSTemplates_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator smsDetails = driver.locator("//*[@id=\"SMSTemplates_listView_row_1\"]/td[2]/span[1]/span");
	private Locator smsEditButton = driver.locator("//*[@id=\"SMSTemplates_detailView_basicAction_LBL_EDIT\"]");
	private Locator addProfileButton = driver.locator("//*[@id=\"Profiles_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator profileDetails = driver.locator("//*[@id=\"listview-table\"]/tbody/tr[1]");
	private Locator editProfile = driver.locator("//*[@id=\"profilePageHeader\"]/div[1]/div[2]/div/button");

	public RoleDetails getPrivileges(String dataFile) throws StreamReadException, DatabindException, IOException {
		log.info("Get Roles Details from file");
		String filePath = getClass().getClassLoader().getResource(dataFile).getPath();
		RoleDetails rolesDetails = (RoleDetails) SkyHighUtil.readDetailsFromFile(filePath, RoleDetails.class);
		return rolesDetails;
	}

	public List<String> getModuleList(RoleDetails rolesDetails, int dataSetID) {
		log.info("Get Modules List");
		Class<?> modules = rolesDetails.getRoles()[dataSetID].getPrivileges().get(0).getClass();
		Field[] fields = modules.getDeclaredFields();
		List<String> moduleList = new ArrayList<>();

		for (Field field : fields) {
			field.setAccessible(true);
			String value = field.getName();
			moduleList.add(value);
			log.info("Module: {}", value);
		}

		return moduleList;
	}

	public void validatePermission(String module, Privileges privileges) {
		switch (CsmartModule.fromDescription(module)) {
		
		case ACCOUNTS:
			log.info("Account Permission Validation");
			selectDashboardOption(privileges.getAccounts().getCsmartMenu());
			Locator accountModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getAccounts().getModule() + "')]");
			validateElement(accountModule, privileges.getAccounts().isView());

			if (privileges.getAccounts().isView() == true) {
				selectSubMenu(privileges.getAccounts().getModule());
				validateElement(accountAddButton, privileges.getAccounts().isCreate());
				new DropdownElement(driver, accountStatusDropdown).select2DropdownSelect(accountStatusField, "Active");
				new ButtonElement(driver, accountNumberField).click();
				new ButtonElement(driver, searchButton).click();
				driver.waitForTimeout(5000);
				new ButtonElement(driver, accountDetails).click();
				driver.waitForTimeout(2000);
				new ButtonElement(driver, switchEyeIcon).click();
				driver.waitForTimeout(2000);
				new ButtonElement(driver, detailsTab).click();
				validateElement(accountEditButton, privileges.getAccounts().isEdit());
				
				if (privileges.getSalesOrder().isView() == true) {
					
				}
			}
			break;

		case INVOICE:
			log.info("Invoice Permission Validation");
			selectDashboardOption(privileges.getInvoice().getCsmartMenu());
			Locator invoiceModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getInvoice().getModule() + "')]");
			validateElement(invoiceModule, privileges.getInvoice().isView());

			if (privileges.getInvoice().isView() == true) {
				selectSubMenu(privileges.getInvoice().getModule());
				validateElement(invoiceAddButton, privileges.getInvoice().isCreate());
				new ButtonElement(driver, invoiceDetails).click();
				new ButtonElement(driver, detailsTab).click();
				validateElement(invoiceEditButton, privileges.getInvoice().isEdit());
			}
			break;

		case CONTACTS:
			log.info("Contacts will be validated under Accounts Module");
			/*
			 * selectDashboardOption(privileges.getContacts().getCsmartMenu()); Locator
			 * contactModule =
			 * driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a" +
			 * "[contains(.,'" + privileges.getContacts().getModule() + "')]");
			 * validateElement(contactModule, privileges.getContacts().isView());
			 * 
			 * if (privileges.getContacts().isView() == true) {
			 * selectSubMenu(privileges.getContacts().getModule());
			 * validateElement(contactsButton, privileges.getContacts().isCreate()); new
			 * ButtonElement(driver, contactDetails).click(); driver.waitForTimeout(2000);
			 * new ButtonElement(driver, detailsTab).click();
			 * validateElement(contactEditButton, privileges.getContacts().isEdit()); }
			 */
			break;

		case MOTD:
			log.info("Message of the Day Permission Validation");
			selectDashboardOption(privileges.getMotd().getCsmartMenu());
			Locator motdModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a" + "[contains(.,'"
					+ privileges.getMotd().getModule() + "')]");
			validateElement(motdModule, privileges.getMotd().isView());

			if (privileges.getMotd().isView() == true) {
				selectSubMenu(privileges.getMotd().getModule());
				validateElement(motdAddButton, privileges.getMotd().isCreate());
				new ButtonElement(driver, motdDetails).click();
				driver.waitForTimeout(2000);
				validateElement(motdEditButton, privileges.getMotd().isEdit());
			}
			break;

		case SALES_ORDER:
			log.info("Sales Order will be validated under Accounts Module");
			/*
			 * selectDashboardOption(privileges.getSalesOrder().getCsmartMenu()); Locator
			 * salesOrderModule =
			 * driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a" +
			 * "[contains(.,'" + privileges.getSalesOrder().getModule() + "')]");
			 * validateElement(salesOrderModule, privileges.getSalesOrder().isView());
			 * 
			 * if (privileges.getSalesOrder().isView() == true) {
			 * selectSubMenu(privileges.getSalesOrder().getModule()); new
			 * DropdownElement(driver,
			 * salesStatusDropdown).select2DropdownSelect(salesCategorySearch, "Approved");
			 * new ButtonElement(driver, salesOrderNumberField).click(); new
			 * ButtonElement(driver, searchButton).click(); driver.waitForTimeout(3000); new
			 * ButtonElement(driver, salesDetails).click(); new ButtonElement(driver,
			 * detailsTab).click(); driver.waitForTimeout(2000);
			 * validateElement(salesEditButton, privileges.getSalesOrder().isEdit());
			 * validateElement(salesSubmitButton, privileges.getSalesOrder().isSubmit()); }
			 * 
			 * break;
			 */
		 

		case TICKETS:
			log.info("Tickets Permission Validation");
			selectDashboardOption(privileges.getTickets().getCsmartMenu());
			Locator ticketsModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getTickets().getModule() + "')]");
			validateElement(ticketsModule, privileges.getTickets().isView());

			if (privileges.getTickets().isView() == true) {
				selectSubMenu(privileges.getTickets().getModule());
				validateElement(ticketAddButton, privileges.getTickets().isCreate());
				new DropdownElement(driver, ticketStatusDropdown).select2DropdownSelect(ticketCategorySearch, "Open");
				new ButtonElement(driver, ticketTitle).click();
				new ButtonElement(driver, searchButton).click();
				driver.waitForTimeout(3000);
				new ButtonElement(driver, ticketDetails).click();
				new ButtonElement(driver, detailsTab).click();
				driver.waitForTimeout(2000);
				validateElement(ticketEditButton, privileges.getTickets().isEdit());
			}
			break;

		case INVENTORY:
			log.info("Inventory Permission Validation");
			selectDashboardOption(privileges.getInventory().getCsmartMenu());
			Locator inventoryModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getInventory().getModule() + "')]");
			validateElement(inventoryModule, privileges.getInventory().isView());

			if (privileges.getInventory().isView() == true) {
				selectSubMenu(privileges.getInventory().getModule());
				validateElement(inventoryAddButton, privileges.getInventory().isCreate());
				new ButtonElement(driver, moreButton).click();
				validateElement(voidOptionButton, privileges.getInventory().isVoidSim());
				moreButton.press("Escape");
				new DropdownElement(driver, inventoryStatusDropdown).select2DropdownSelect(inventoryStatusSearchField, "Available");
				new ButtonElement(driver, inventoryNo).click();
				new ButtonElement(driver, searchButton).click();
				driver.waitForTimeout(2000);
				new ButtonElement(driver, inventoryDetails).click();
				validateElement(inventoryEditButton, privileges.getInventory().isEdit());
			}
			break;

		case VOUCHER:
			log.info("Voucher Permission Validation");
			selectDashboardOption(privileges.getVoucher().getCsmartMenu());
			Locator voucherModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getVoucher().getModule() + "')]");
			validateElement(voucherModule, privileges.getVoucher().isView());

			if (privileges.getVoucher().isView() == true) { // create and allocate
				selectSubMenu(privileges.getVoucher().getModule());
				new ButtonElement(driver, moreButton).click();
				validateElement(voidOptionButton, privileges.getVoucher().isVoidVoucher());
				validateElement(generateButton, privileges.getVoucher().isGenerateVoucher());
				validateElement(bulkStatusUpdateOption, privileges.getVoucher().isBulkStatusUpdate());
				validateElement(activateVoucherButton, privileges.getVoucher().isBulkStatusUpdate());
				validateElement(exportButton, privileges.getVoucher().isBulkStatusUpdate());
				moreButton.press("Escape");
				driver.waitForTimeout(2000);
				new DropdownElement(driver, rechargeStatusDropdown).select2DropdownSelect(rechargeSearchField, "Available");
				new ButtonElement(driver, voucherNoField).click();
				new ButtonElement(driver, searchButton).click();
				driver.waitForTimeout(2000);
				new ButtonElement(driver, voucherDetails).click();
				new ButtonElement(driver, detailsTab).click();
				validateElement(voucherEditButton, privileges.getVoucher().isEdit());
			}
			break;

		case DOCUMENTS:
			log.info("Documents Permission Validation");
			selectDashboardOption(privileges.getDocuments().getCsmartMenu());
			Locator documentModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getDocuments().getModule() + "')]");
			validateElement(documentModule, privileges.getDocuments().isView());

			if (privileges.getDocuments().isView() == true) { // create and allocate
				selectSubMenu(privileges.getDocuments().getModule());
				validateElement(documentAddButton, privileges.getDocuments().isCreate());
				new ButtonElement(driver, documentDetails).click();
				new ButtonElement(driver, detailsTab).click();
				validateElement(documentEditButtom, privileges.getDocuments().isEdit());

			}
			break;

		case SECURITY_QUESTIONS:
			log.info("Security Questions Permission Validation");
			selectDashboardOption(privileges.getSecurityQuestions().getCsmartMenu());
			Locator securityModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getSecurityQuestions().getModule() + "')]");
			validateElement(securityModule, privileges.getSecurityQuestions().isView());

			if (privileges.getSecurityQuestions().isView() == true) { // create and
				selectSubMenu(privileges.getSecurityQuestions().getModule());
				validateElement(securityQuestionAddButton, privileges.getSecurityQuestions().isCreate());
				new ButtonElement(driver, securityQuestionDetails).click();
				new ButtonElement(driver, detailsTab).click();
				validateElement(securityQuestionEditButton, privileges.getSecurityQuestions().isEdit());

			}
			break;

		case NOTES_TEMPLATES:
			log.info("Notes Templates Permission Validation");
			selectDashboardOption(privileges.getNotesTemplates().getCsmartMenu());
			Locator notesTemplateModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getNotesTemplates().getModule() + "')]");
			validateElement(notesTemplateModule, privileges.getNotesTemplates().isView());

			if (privileges.getNotesTemplates().isView() == true) {
				selectSubMenu(privileges.getNotesTemplates().getModule());
				validateElement(templateAddButton, privileges.getNotesTemplates().isCreate());
				new ButtonElement(driver, templateDetails).click();
				validateElement(templateEditButton, privileges.getNotesTemplates().isEdit());

			}
			break;

		case REPORTS:
			log.info("Reports Permission Validation");
			selectDashboardOption(privileges.getReports().getCsmartMenu());
			Locator reportModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getReports().getModule() + "')]");
			validateElement(reportModule, privileges.getReports().isView());

			if (privileges.getReports().isView() == true) {
				selectSubMenu(privileges.getReports().getModule());
				validateElement(reportAddButton, privileges.getReports().isCreate());
				new ButtonElement(driver, reportDetails).click();
				validateElement(reportEditButton, privileges.getReports().isEdit());

			}
			break;

		case MNP:
			log.info("MNP Permission Validation");
			selectDashboardOption(privileges.getMnp().getCsmartMenu());
			Locator mnpModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a" + "[contains(.,'"
					+ privileges.getMnp().getModule() + "')]");
			validateElement(mnpModule, privileges.getMnp().isView());

			if (privileges.getMnp().isView() == true) {
				selectSubMenu(privileges.getMnp().getModule());
				validateElement(mnpAddButton, privileges.getMnp().isCreate());

			}
			break;

		case USER_PERMISSION:
			log.info("User Permission Validation");
			selectDashboardOption(privileges.getUserPermissions().getCsmartMenu());
			Locator userModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a" + "[contains(.,'"
					+ privileges.getUserPermissions().getModule() + "')]");
			validateElement(userModule, privileges.getUserPermissions().isView());

			if (privileges.getUserPermissions().isView() == true) {
				selectSubMenu(privileges.getUserPermissions().getModule());
				validateElement(addUserButton, privileges.getUserPermissions().isCreate());
				new ButtonElement(driver, userDetails).click();
				driver.waitForTimeout(2000);
				validateElement(editUserButton, privileges.getUserPermissions().isEdit());
				navigateSubModules("User Management", "Profile");
				new ButtonElement(driver, userDetails).click();

				validateElement(addProfileButton, privileges.getUserPermissions().isEdit());
				new ButtonElement(driver, profileDetails).click();
				new ButtonElement(driver, editProfile).click();
			}
			break;

		case PAYMENTS:
			log.info("Payments Permission Validation");
			selectDashboardOption(privileges.getPayments().getCsmartMenu());
			Locator paymentsModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getPayments().getModule() + "')]");
			validateElement(paymentsModule, privileges.getPayments().isView());

			if (privileges.getPayments().isView() == true) { // create and allocate
				selectSubMenu(privileges.getPayments().getModule());
				validateElement(paymentsAddButton, privileges.getPayments().isCreate());
				new DropdownElement(driver, paymentsStatusDropdown).select2DropdownSelect(paymentsStatusField,
						"Initialized");
				new ButtonElement(driver, paymentDateField).click();
				new ButtonElement(driver, searchButton).click();
				new ButtonElement(driver, paymentElipsis).click();
				validateElement(paymentEditOption, privileges.getPayments().isEdit());
				driver.waitForTimeout(3000);
			}
			break;

		case FAQ:
			log.info("FAQ Permission Validation");
			selectDashboardOption(privileges.getFaq().getCsmartMenu());
			Locator faqModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a" + "[contains(.,'"
					+ privileges.getFaq().getModule() + "')]");
			validateElement(faqModule, privileges.getFaq().isView());

			if (privileges.getFaq().isView() == true) { // create and allocate missing
				selectSubMenu(privileges.getFaq().getModule());
				navigateSubModules("Tools", "FAQ");
				validateElement(faqAddButton, privileges.getFaq().isCreate());
				driver.waitForTimeout(2000);
				new ButtonElement(driver, faqDetails).click();
				validateElement(faqEditButton, privileges.getFaq().isEdit());
			}
			break;

		case TRANSLATIONS:
			log.info("Translations Permission Validation");
			selectDashboardOption(privileges.getTranslations().getCsmartMenu());
			Locator translationsModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getTranslations().getModule() + "')]");
			validateElement(translationsModule, privileges.getTranslations().isView());

			if (privileges.getTranslations().isView() == true) { // create and allocate
				selectSubMenu(privileges.getTranslations().getModule());
				navigateSubModules("Tools", "Translations");
				validateElement(translationsAddButton, privileges.getTranslations().isCreate());
				new ButtonElement(driver, translationsDetails).click();
				new ButtonElement(driver, detailsTab).click();
				validateElement(translationsEditButton, privileges.getTranslations().isEdit());
			}
			break;

		case VENDORS:
			log.info("Vendors Permission Validation");
			selectDashboardOption(privileges.getVendors().getCsmartMenu());
			Locator vendorModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getVendors().getModule() + "')]");
			validateElement(vendorModule, privileges.getVendors().isView());

			if (privileges.getVendors().isView() == true) {
				selectSubMenu(privileges.getVendors().getModule());
				navigateSubModules("Tools", "Translations");
				new ButtonElement(driver, vendorIcon).click();
				validateElement(vendorAddButton, privileges.getVendors().isCreate());
				new ButtonElement(driver, vendorDetails).click();
				new ButtonElement(driver, detailsTab).click();
				validateElement(vendorEditButton, privileges.getVendors().isEdit());
			}
			break;

		case PRODUCT_CATALOGUES:
			log.info("Product Catalogue Permission Validation");
			selectDashboardOption(privileges.getProductCatalogue().getCsmartMenu());
			Locator productCatalogueModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getProductCatalogue().getModule() + "')]");
			validateElement(productCatalogueModule, privileges.getProductCatalogue().isView());

			if (privileges.getProductCatalogue().isView() == true) {
				selectSubMenu(privileges.getProductCatalogue().getModule());
				navigateSubModules("Product Catalogue", "Products");
				validateElement(productAddButton, privileges.getProductCatalogue().isCreate());
				new ButtonElement(driver, productDetails).click();
				validateElement(productEditButton, privileges.getProductCatalogue().isEdit());
				new ButtonElement(driver, packageIcon).click();
				validateElement(packageAddButton, privileges.getProductCatalogue().isCreate());
				new ButtonElement(driver, packageDetails).click();
				new ButtonElement(driver, detailsTab).click();
				validateElement(packageEditButton, privileges.getProductCatalogue().isEdit());
				new ButtonElement(driver, policyIcon).click();
				validateElement(policyAddButton, privileges.getProductCatalogue().isCreate());
				new ButtonElement(driver, policyDetails).click();
				new ButtonElement(driver, detailsTab).click();
				driver.waitForTimeout(2000);
				validateElement(policyEditButton, privileges.getProductCatalogue().isEdit());
			}
			break;

		case CAMPAIGNS:
			log.info("Campaigns Permission Validation");
			selectDashboardOption(privileges.getCampaigns().getCsmartMenu());
			Locator campaignModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getCampaigns().getModule() + "')]");
			validateElement(campaignModule, privileges.getCampaigns().isView());

			if (privileges.getCampaigns().isView() == true) {
				selectSubMenu(privileges.getCampaigns().getModule());
				validateElement(campaignAddButton, privileges.getCampaigns().isCreate());
				new ButtonElement(driver, campaignDetails).click();
				validateElement(campaignEditButton, privileges.getCampaigns().isEdit());
			}
			break;

		case TEMPLATES:
			log.info("Templates Permission Validation");
			selectDashboardOption(privileges.getTemplates().getCsmartMenu());
			Locator templateModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getTemplates().getModule() + "')]");
			validateElement(templateModule, privileges.getTemplates().isView());

			if (privileges.getTemplates().isView() == true) {
				selectSubMenu(privileges.getTemplates().getModule());
				navigateSubModules("Tools", "Email Templates");
				validateElement(emailAddButton, privileges.getTemplates().isCreate());
				new ButtonElement(driver, emailDetails).click();
				validateElement(emailEditButton, privileges.getTemplates().isEdit());
				new ButtonElement(driver, smsButton).click();
				validateElement(smsAddButton, privileges.getTemplates().isCreate());
				new ButtonElement(driver, smsDetails).click();
				validateElement(smsEditButton, privileges.getTemplates().isEdit());
			}
			break;

		case TRANSACTIONS:
			log.info("Transactions Permission Validation");
			selectDashboardOption(privileges.getTransactions().getCsmartMenu());
			Locator transactionsModule = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a"
					+ "[contains(.,'" + privileges.getTransactions().getModule() + "')]");
			validateElement(transactionsModule, privileges.getTransactions().isView());

			if (privileges.getTransactions().isView() == true) {

			}
			break;

		case STATUS_PAGE:
		case CHOWN:
		case CHOWN_DOMESTIC:
		case HRCT_SVC:
			log.info("HRCT Permission Validation");

			if (privileges.getUserPermissions().isView()) {
				if (privileges.getHrctSVC().isAccess() == true) {

				}
			} else {
				log.info("User has no access to CRM Settings");
			}
			break;

		default:
			Assert.fail("Invalid Module Name");
		}

	}

	private void validateElement(Locator element, boolean value) {
		log.info("Validate Element if it is expected or not");
		try {
			if (value == true) {
				driver.waitForTimeout(4000);
				assertThat("Element should be visible", element.isVisible(), equalTo(true));
			} else {
				log.info("Element is not visible");
				driver.keyboard().press("Escape");
				driver.waitForTimeout(2000);
			}
		} catch (PlaywrightException e) {
			log.info("Element not found");
		}

	}

	private void navigateSubModules(String module, String subModule) {

		if (!("Product Catalogue".equalsIgnoreCase(module) || "User Management".equalsIgnoreCase(module))) {
			module = module.toUpperCase();
		}

		driver.locator("//span[contains(.,'" + module + "')]").first().click();
		Locator pageNameField = driver.locator("//a[contains(.,'" + subModule + "')]").first();
		driver.waitForTimeout(3000);
		new ButtonElement(driver, pageNameField).click();
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}
}
