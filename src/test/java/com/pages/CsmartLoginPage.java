package com.pages;

import org.junit.Assert;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import java.io.IOException;
import com.elements.ButtonElement;
import com.elements.TextElement;
import com.elements.TextfieldElement;
import com.microsoft.playwright.FrameLocator;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;
import com.utils.SkyHighUtil;

public class CsmartLoginPage extends SkyHighPage {
	
   	private Locator loginButton = driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Sign in"));
	private Locator usernameField = driver.locator("//*[@name='username']");
	private Locator passwordField = driver.locator("//*[@name='password']");
	private Locator userProfile = driver.locator("//*[@id=\"navbar\"]/ul/li[7]/div/a/span[1]");
	private Locator logOutButton = driver.locator("//*[@id=\"menubar_item_right_LBL_SIGN_OUT\"]");
	@SuppressWarnings("deprecation")
	FrameLocator checkboxFrame = driver.frameLocator("iframe[src*='recaptcha']").first();
	private Locator profileButton = driver.locator(".userName");
	private Locator userNameText = driver.locator(".profile-container > h4");
	private Locator invalidCaptcha = driver.locator("//*[@id='loginDiv']//div[@class='panel-body ']//div[@class='alert alert-danger']");
	private String url;

    public CsmartLoginPage(Page driver) {
    	super(driver);
    }
    
	public void setCsmartURL(String csmartURL) {
		this.url = csmartURL;	
	}
   
	public void enterURL() {
		log.info("CSmart URL: {}", url);
		driver.navigate(url);
	}
	
	public void verifyPageStatus() throws IOException {
		log.info("Verify if Page is Accessible");
		
		int responseCode = SkyHighUtil.getResponseCode(url);
		if (!(responseCode > 400)) {
			log.info("CSmart Website is accessible. Proceed to Login");
		 } else {
			Assert.fail("CSmart Website is Not Accessible");
		}
	}


	public void verifyUserLoggedInCsmart(String userName, String password) throws IOException {
		log.info("Verification User Logged In CSmart");
		if (usernameField.isVisible()) {
			new TextfieldElement(driver, usernameField).setValue(userName);
			new TextfieldElement(driver, passwordField).setSensitiveValue(password);
			driver.waitForTimeout(2000);
			//performCaptcha();
			new ButtonElement(driver, loginButton).click();
			
			if (invalidCaptcha.isVisible()) {
				System.out.println("Invalid Captcha found");
				new TextfieldElement(driver, usernameField).setValue(userName);
				new TextfieldElement(driver, passwordField).setSensitiveValue(password);
				driver.waitForTimeout(2000);
				new ButtonElement(driver, loginButton).click();}

		} else {
			log.info("User is already logged in to CSmart");
		}
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}


	public void logoutApplication() {
		log.info("User logs out from Csmart application");
		new ButtonElement(driver, userProfile).click();
		new ButtonElement(driver, logOutButton).click();
		assertThat("Csmart Login Page is not displayed", usernameField.isVisible(), equalTo(true));
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public String getUserName() {
		log.info("Get Profile User");
		new ButtonElement(driver, profileButton).click();
		driver.waitForTimeout(2000);
		String userName = new TextElement(driver, userNameText).getText();
		new ButtonElement(driver, profileButton).click();
		return userName;
	}
	
	public void performCaptcha() {
		log.info("Perform Captcha Validation");
		boolean isCaptchaPresent = driver.locator("iframe[src*='recaptcha']").count() > 0;
		
		if (isCaptchaPresent) 
			 checkboxFrame.locator("#recaptcha-anchor").click();
				driver.pause();
	}
}
