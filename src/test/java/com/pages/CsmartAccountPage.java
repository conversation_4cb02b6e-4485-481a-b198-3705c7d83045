package com.pages;


import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import java.io.IOException;
import com.customer.CustomerDetails;
import com.customer.PrimaryContactDetails;
import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.elements.TextfieldElement;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;
import com.stepDefinition.GlobalVariables;
import com.utils.SkyHighUtil;

public class CsmartAccountPage extends SkyHighPage {
	
    public CsmartAccountPage(Page driver) {
    	super(driver);
    }
    
	private Locator addAccountButton = driver.locator("//*[@id=\"Accounts_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator titleDropdown = driver.locator("#s2id_autogen3");
	private Locator titleSearchField = driver.locator("#s2id_autogen4_search");
	private Locator firstNameField = driver.locator("//*[@id=\"Accounts_editView_fieldName_firstname\"]");
	private Locator lastNameField = driver.locator("//*[@id=\"Accounts_editView_fieldName_lastname\"]");
	private Locator middleNameField = driver.locator("//*[@id=\"Accounts_editView_fieldName_middlename\"]");
	private Locator emailField = driver.locator("//*[@id=\"Accounts_editView_fieldName_email1\"]");
	private Locator addressSuggestionDropdown = driver.locator("#s2id_autogen77");
	private Locator addressSuggestionField = driver.locator("#s2id_autogen78_search");
	private Locator pinField = driver.locator("//*[@id=\"Accounts_editView_fieldName_telephonepin\"]");
	private Locator confirmPinField = driver.locator("//*[@id=\"confirm_telephonepin\"]");
	private Locator dobField = driver.locator("//*[@id=\"Accounts_editView_fieldName_dob\"]");
	private Locator accountNumberText = driver.locator("//label[@title='Account Number']//following::td[1]/div/span");
	private Locator accountNumberSearch = driver.locator("//*[@name='account_no']");
	private Locator searchButton = driver.locator("//button[@class='btn btn-success btn-sm']");
	private Locator accountStatusText = driver.locator("//*[@id=\"Accounts_listView_row_1\"]/*[@data-name='accountstatus']");
	private Locator emptyRecordMessage = driver.locator(".emptyRecordsContent");
	private Locator saveButton = driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Save"));
	private Locator contactField = driver.locator("//*[@id=\"Accounts_editView_fieldName_phone\"]");
	private Locator securityQuestionsDropdowns = driver.locator("select[name='questionField[]']");
	
	private final static String SECURITY_ANSWER= "Regression";
	
	
	public void createCustomerAccount() {
		log.info("Create Customer Account");
		driver.waitForLoadState();
		new ButtonElement(driver, addAccountButton).click();
	}
	
	public void enterCustomerDetails(String fileLocation) throws StreamReadException, DatabindException, IOException {
		log.info("Enter Customer Details");
		String filePath = getClass().getClassLoader().getResource(fileLocation).getPath();
		CustomerDetails customerDetails = (CustomerDetails) SkyHighUtil.readDetailsFromFile(filePath, CustomerDetails.class);
		
		PrimaryContactDetails primaryContactDetails = customerDetails.getTestData()[0].getData().get(0).getPrimaryContactDetails();
		new DropdownElement(driver, titleDropdown).select2DropdownDialogBox(titleSearchField, primaryContactDetails.getTitle());
		new TextfieldElement(driver, firstNameField).setValue(primaryContactDetails.getFirstName());
		new TextfieldElement(driver, lastNameField).setValue(primaryContactDetails.getLastName());
		new TextfieldElement(driver, contactField).setValue(primaryContactDetails.getContactNumber().substring(1));
		new TextfieldElement(driver, middleNameField).setValue(primaryContactDetails.getMiddleName());
		new TextfieldElement(driver, emailField).setValue("csmart-" + SkyHighUtil.generateRandomNumber(1000) + "@gmail.com");
		new TextfieldElement(driver, dobField).setValue(primaryContactDetails.getDateOfBirth());
		new DropdownElement(driver, addressSuggestionDropdown).select2DropdownDialogBox(addressSuggestionField, customerDetails.getTestData()[0].getData().get(0).getAddressDetails().getAddress());
		new TextfieldElement(driver, pinField).setValue(primaryContactDetails.getPinCode());
		new TextfieldElement(driver, confirmPinField).setValue(primaryContactDetails.getConfirmPinCode());
		completeSecurityQuestions();
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
		new ButtonElement(driver, saveButton).click();
	}

	public void validateAccountStatus(String accountStatus) {
		log.info("Validate Account Status");
		new TextfieldElement(driver, accountNumberSearch).setValue(GlobalVariables.getAccountNumber());
		new ButtonElement(driver, searchButton).click();
		driver.waitForTimeout(2000);
		assertThat("No result is found", emptyRecordMessage.isVisible(), equalTo(false));
		assertThat("Account is not active", accountStatusText.innerText().trim(), equalTo(accountStatus));
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

	public void getAccountNumber() {
		log.info("Get Account Number");
		GlobalVariables.setAccountNumber(accountNumberText.innerText().trim());
	}
	
	public void completeSecurityQuestions() {
		log.info("Complete Security Questions");

		for (int i = 0; i < securityQuestionsDropdowns.count(); i++) {
			Locator securityOptionsDropdown = securityQuestionsDropdowns.nth(i);
			Locator securityOptions = securityOptionsDropdown.locator("option");
			int optionIndexToSelect = (i + 1);

			if (optionIndexToSelect < securityOptions.count()) {
				Locator selectedOption = securityOptions.nth(optionIndexToSelect);
				securityOptionsDropdown.selectOption(selectedOption.textContent());
				Locator answerField = driver.locator("//*[@name='answerField[]']").nth(i);
				new TextfieldElement(driver, answerField).setSensitiveValue(SECURITY_ANSWER);
			}
		}
	}
}
