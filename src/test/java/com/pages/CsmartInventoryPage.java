	package com.pages;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.assertj.core.api.SoftAssertions;
import org.junit.Assert;
import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.elements.TextfieldElement;
import com.enums.HeadersList;
import com.enums.InventoryColumns;
import com.enums.ResourceType;
import com.inventory.InventoryDetails;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Locator.ClickOptions;
import com.microsoft.playwright.Locator.HoverOptions;
import com.opencsv.exceptions.CsvValidationException;
import com.utils.SkyHighUtil;

public class CsmartInventoryPage extends SkyHighPage {

	public CsmartInventoryPage(Page driver) {
		super(driver);

	}
	
	List<String> itemList = new ArrayList<String>();
	Map<String, String> filenameMap = new HashMap<String, String>();
	private String filePath;
	String handlingMethod;
	String userName;
	List<InventoryDetails> inventoryDetailsList;
	List<InventoryDetails> inventoryDetailsFromFile;
	Path csvFilePath;
	String searchColumn;

	private final static String CARRIER= "Telstra";
	private final static String VOUCHER= "Request for Vouchers";
	private final static String EXPECTED_STATUS= "Available";
	private final static String STARTER_PACK= "$5 PAYG Plan";
	
    ClickOptions cOptions = new ClickOptions();
    HoverOptions hOptions = new HoverOptions();
    
	//inventoryPage
	private Locator inventoryStatusDropdown = driver.locator("#s2id_autogen1");
	private Locator inventoryStatusSearchField = driver.locator("#s2id_autogen1 .select2-input");
	private Locator simTypeDropdownField = driver.locator("#s2id_autogen5");
	private Locator simTypeSearchField = driver.locator("#s2id_autogen5 .select2-input");
	private Locator inventorySortButton = driver.locator("//*[@id=\"listview-table\"]/thead/tr[1]/th[2]/a/i | //*[@id=\"listview-table\"]/thead/tr[1]/th[2]/a/i");
	private Locator inventorySearchButton = driver.locator("//*[@id=\"listview-table\"]/thead/tr[2]/th[1]/div/button");
	private Locator iccIdField = driver.locator("//*[@name='iccid']");
	private Locator emptyRecordMessage = driver.locator(".emptyRecordsContent");
	private Locator moreButton = driver.locator("//*[@id=\"listview-actions\"]/div/div[1]/div/div/button");
	private Locator yesVoidButton = driver.locator("//button[contains(.,'Yes')]");
	private Locator voidOptionButton = driver.locator("//*[@id=\"Inventory1_listView_advancedAction_Void_SIM\"] | //*[@id=\"RechargeVoucher_listView_advancedAction_Void_Voucher\"]");
	private Locator inventoryStatusResult = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='istatus'] | //*[@id=\"RechargeVoucher_listView_row_1\"]/*[@data-name='voucherstatus']");
	private Locator categoryDropdown = driver.locator("#s2id_autogen1");
	private Locator categorySearch = driver.locator("#s2id_autogen2");
	private Locator clearResultButton = driver.locator("//*[@id=\"clearSearchResult\"]");
	private Locator assignedToResult = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='assigned_user_id']");
	private Locator createdTimeResult = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='createdtime']");
	private Locator iccidResult = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='iccid']");
	private Locator imsiResult = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='imsi1']");
	private Locator expiryDateResult = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='expirydate']");
	private Locator activationCodeResult = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='activationcode']");
	private Locator simNumberResult = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='sim']");
	private Locator voucherAssignedTo = driver.locator("#s2id_autogen5");
	private Locator voucherAssignedSearch = driver.locator("#s2id_autogen5 .select2-input");
	private Locator inventoryAssignedTo = driver.locator("#s2id_autogen8"); 
	private Locator inventoryAssignedSearch = driver.locator("#s2id_autogen7 .select2-input");
	private Locator modifiedTime = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='modifiedtime']");
	
	//bulkStatusUpdate
	private Locator bulkStatusUpdateOption = driver.locator("//a[contains(text(),'Bulk Status Update')]");
	private Locator statusFromDropdown = driver.locator("#s2id_status_from");
	private Locator statusFromSearchField = driver.locator(".select2-drop-active .select2-input");
	private Locator statusToDropdown = driver.locator("#s2id_status_to");
	private Locator statusToSearchField = driver.locator(".select2-drop-active .select2-input").last();
	private Locator submitButton = driver.locator(".btn-lg");
	
	//uploadPage
	private Locator importNextStepButton = driver.locator("//*[@id=\"importStep2\"]");
	private Locator mergeTypeDropdown = driver.locator("//*[@id=\"s2id_merge_type\"]");
	private Locator mergeTypeSearch = driver.locator(".select2-drop-active .select2-input");
	private Locator submitDuplicatePage = driver.locator("//*[@id=\"uploadAndParse\"]");
	private Locator submitImport = driver.locator("//*[@id=\"importButton\"]");
	private Locator simTextField = driver.locator("//*[@name='sim']");
	private Locator msnTextField = driver.locator("//*[@name='imsisdn']");
	private Locator importSummary = driver.locator("//*[@id=\"overlayPageContent\"]/div[2]/div/div[1]/div/div/h4");
	private Locator finishButton = driver.locator("//button[contains(.,'Finish')]");
	private Locator listViewContent = driver.locator("//*[@id=\"listViewContent\"]");
	private Locator carrierDropdown = driver.locator("#s2id_carrier_type");
	private Locator carrierSearch = driver.locator(".select2-drop-active .select2-input").last();
	private Locator starterKitDropdown = driver.locator("#s2id_product_type");
	private Locator starterKitSearch = driver.locator(".select2-drop-active .select2-input").last();
	private Locator simTypeDropdown = driver.locator("#s2id_sim_type");
	private Locator simTypeSearch = driver.locator(".select2-drop-active .select2-input");
	private Locator importAssignedDropdown = driver.locator("#s2id_userId");
	private Locator importAssignedUserSearch = driver.locator(".select2-drop-active .select2-input");
	private Locator importButton = driver.locator("//*[@id=\"Inventory1_basicAction_LBL_IMPORT\"]");
	private Locator simRadioButton = driver.locator("//*[@id=\"simormsisdn_container\"]/td[2]/label/input[@name='simormsisdn' and @value='sim']");
	private Locator msnRadioButton = driver.locator("//*[@id=\"simormsisdn_container\"]/td[2]/label/input[@name='simormsisdn' and @value='msisdn']");
	private Locator selectFileButton = driver.locator("//*[@id=\"import_file\"]");
	private Locator voucherStatusDropdown = driver.locator("#s2id_autogen3");
	private Locator voucherStatusSearch = driver.locator("#s2id_autogen4");
	private Locator voucherControlID = driver.locator("//*[@name='vouchercode']");
	private Locator activationCode = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/td[4]/span[1]/span");
	private Locator starterPack = driver.locator("//*[@id=\"Inventory1_listView_row_1\"]/*[@data-name='staterpackage']");
	private Locator csvFileButton = driver.locator("//*[@id=\"csvImport\"]/div/div/h4");
	private Locator successResult = driver.locator("//div[@class='result-number success']");
	private Locator closeButton = driver.locator("//div[@class='overlayHeader']//span[@class='fa fa-close']");

	public void uploadImportFile(String resourceType) throws CsvValidationException, IOException {
		log.info("Upload Import File in Import Inventory");
		new ButtonElement(driver, importButton).click();
		driver.waitForTimeout(3000);
		new ButtonElement(driver, csvFileButton).click();
		
		if (ResourceType.MSN.getDescription().equalsIgnoreCase(resourceType)) {
			new ButtonElement(driver, msnRadioButton).click();
		} else {
			new ButtonElement(driver, simRadioButton).click();
			new DropdownElement(driver, simTypeDropdown).select2DropdownDialogBox(simTypeSearch, resourceType);
		}
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
		driver.waitForTimeout(2000);
		uploadFile();
	}

	public void voidProduct(int productCount, String oldStatus, String newStatus) {
		log.info("Void Sim or Voucher");
	
	  	cOptions.setForce(true);
        cOptions.setTimeout(2000);
		
		new ButtonElement(driver, moreButton).click();
		if (productCount == 1) {
			new ButtonElement(driver, voidOptionButton).click();
			driver.waitForTimeout(2000);
			new ButtonElement(driver, yesVoidButton, true).click();
		} else {
			new ButtonElement(driver, bulkStatusUpdateOption).click();
			driver.waitForTimeout(2000);
			new DropdownElement(driver, statusFromDropdown).select2DropdownDialogBox(statusFromSearchField, oldStatus);
			driver.waitForTimeout(2000);
			new DropdownElement(driver, statusToDropdown).select2DropdownDialogBox(statusToSearchField, newStatus);
			SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
			driver.waitForTimeout(2000);
			new ButtonElement(driver, submitButton, true).click();
		}
	
		driver.waitForTimeout(10000);
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

	public void searchData(String productType, String initialStatus) {
		log.info("Search Valid Status for {} ", productType);
		
		if ("Physical SIM".equalsIgnoreCase(productType) || "ESIM".equalsIgnoreCase(productType)) {
			new DropdownElement(driver, inventoryStatusDropdown).select2DropdownSelect(inventoryStatusSearchField, initialStatus);
			new DropdownElement(driver, inventoryAssignedTo).select2DropdownSelect(inventoryAssignedSearch, getUserName());
			new DropdownElement(driver, simTypeDropdownField).select2DropdownSelect(simTypeSearchField, productType);
			new DropdownElement(driver, iccIdField).click();
			
		} else {
			new DropdownElement(driver, categoryDropdown).select2DropdownSelect(categorySearch, VOUCHER);
			new DropdownElement(driver, voucherStatusDropdown).select2DropdownSelect(voucherStatusSearch, initialStatus);
			new DropdownElement(driver, voucherAssignedTo).select2DropdownSelect(voucherAssignedSearch, getUserName());
			new DropdownElement(driver, voucherControlID).click();
		}
		new ButtonElement(driver, inventorySearchButton).click();
		driver.waitForTimeout(10000);
		new ButtonElement(driver, inventorySortButton).click();
		assertThat("No result is found", emptyRecordMessage.isVisible(), equalTo(false));
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());

	}

	public void validateNewInventoryStatus(String searchColumn, String newStatus) {
		log.info("Validate New Status of Product");
		
		driver.waitForTimeout(5000);
		new ButtonElement(driver,clearResultButton).click();
		driver.waitForTimeout(5000);
		for (int i = 0; i < itemList.size(); i++) {
			driver.waitForTimeout(5000);
			searchInventoryData(searchColumn, itemList.get(i));
			driver.waitForTimeout(5000);
			log.info("Voucher/SIM {} is in {} status", itemList.get(i), inventoryStatusResult.innerText().trim());
			assertThat("Voucher/SIM is not successfully void", inventoryStatusResult.innerText().trim(), equalTo(newStatus));
		}
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}
	
	public void selectProduct(String productType, int productCount) {
	    log.info("Select {} Product(s)", productCount);
	    itemList = new ArrayList<>();
		String data;
		
	    String listViewName = getListViewName(productType);
	    for (int i = 0; i < productCount; i++) {
	        String rowId = listViewName + (i + 1);
	        Locator selectProductLocator = driver.locator("//*[@id=\"" + rowId + "\"]/td[1]/div/span[1]");
	        new ButtonElement(driver, selectProductLocator).click();
	        
			int columnIndex = "Voucher".equalsIgnoreCase(productType) ? 7 : 3;
			String xpath = String.format("//*[@id=\"%s%d\"]/td[%d]/span/span", listViewName, i + 1, columnIndex);
			data = driver.locator(xpath).innerText();
			log.info("{}: {}", productType.toUpperCase(), data);
			itemList.add(data);
			
	    }

	    log.info("Inventory List: {}", itemList);
	    SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}
	
	public void createCSVFile(int count, String productType, String bulkAction) {
			log.info("Create CSV file for Bulk Operation");
		 	itemList = new ArrayList<>();
		 	List<String> headerList = new ArrayList<>();
			List<List<String>> itemList = new ArrayList<>();
			String listViewName = getListViewName(productType);
			String data;
			
			headerList = HeadersList.valueOf(bulkAction.toUpperCase().replace(" ", "")).getHeaders();
			
			for (int i = 0; i < count; i++) {
				List<String> row = new ArrayList<>();
				int columnIndex = "Voucher".equalsIgnoreCase(productType) ? 7 : 3;
				String xpath = String.format("//*[@id=\"%s%d\"]/td[%d]/span/span", listViewName, i + 1, columnIndex);
				data = driver.locator(xpath).innerText();
				log.info("{}: {}", productType.toUpperCase(), data);
				row.add(data);
				itemList.add(row);
			}
			
			String fileName = "/bulk_operation_" + bulkAction.toLowerCase() + ".csv";
			this.filePath = SkyHighUtil.generateFile(headerList, fileName, itemList);
			log.info("File Path is {}", this.filePath);
			filenameMap.put(bulkAction, fileName);
	}
	
	public void createCSVFile(int count, String resourceType) {
		log.info("Create CSV File for upload scenarios");
		List<List<String>> itemList = generateResourceList(count, resourceType);
		List<String> headerList = Arrays.asList("MSN");
		String fileName = "/skyhigh/skyhigh." + resourceType.toLowerCase().replace(" ", "") + ".upload.csv";
		
		headerList = HeadersList.valueOf(resourceType.toUpperCase().replace(" ", "")).getHeaders();
		this.filePath =  SkyHighUtil.generateFile(headerList, fileName, itemList);
	}
	
    
	private String getListViewName(String productType) {
	    return "Physical SIM".equalsIgnoreCase(productType) ? "Inventory1_listView_row_" : "RechargeVoucher_listView_row_";
	}
	
	public void uploadFile() {
	    log.info("Upload File from Computer");
		this.csvFilePath = Paths.get(this.filePath);
		
		if (Files.exists(csvFilePath)) {
			log.info("File found: {}", csvFilePath.toAbsolutePath());
			selectFileButton.setInputFiles(csvFilePath);
		} else {
			log.error("File not found: {} ", csvFilePath.toAbsolutePath());
		}
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

	public void validateImportedData(String resourceType) {
		log.info("Validate Imported File");
		driver.waitForCondition(() -> listViewContent.isVisible());
		driver.waitForTimeout(5000);
		for (int i = 0; i < inventoryDetailsFromFile.size(); i++) {
			String searchData = getSearchData(inventoryDetailsFromFile, resourceType, i);
			searchInventoryData(this.searchColumn, searchData);
			assertThat("No result is found", emptyRecordMessage.isVisible(), equalTo(false));
			validateDuplicateHandling(resourceType, searchData, getUserName());
			SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
		}
	}
	
	private void validateDuplicateHandling(String resourceType, String searchData, String user) {
		log.info("Validate Duplicate Handling");
		SoftAssertions softAssertions = new SoftAssertions();
		
		for (int i = 0; i < inventoryDetailsList.size(); i++) {
			
			if (searchData.equals(getSearchData(inventoryDetailsList, resourceType, i))) {
				
				Boolean isDataNew = inventoryDetailsList.get(i).getIsDataNew();

				if (isDataNew == true || (isDataNew == false && "Overwrite".equalsIgnoreCase(this.handlingMethod))) {
					softAssertions.assertThat(inventoryStatusResult.getAttribute("title")).isEqualTo(EXPECTED_STATUS);
					softAssertions.assertThat(assignedToResult.getAttribute("title")).isEqualTo(user);
					softAssertions.assertThat(createdTimeResult.getAttribute("title")).contains(SkyHighUtil.getCurrentDateTime("dd-MM-yyyy"));
					softAssertions.assertThat(modifiedTime.getAttribute("title")).contains(SkyHighUtil.getCurrentDateTime("dd-MM-yyyy"));
					if (!ResourceType.MSN.getDescription().equalsIgnoreCase(resourceType)) {
						softAssertions.assertThat(iccidResult.getAttribute("title")).isEqualTo(inventoryDetailsFromFile.get(i).getIccID());
						softAssertions.assertThat(imsiResult.getAttribute("title")).isEqualTo(inventoryDetailsFromFile.get(i).getImsi());
						softAssertions.assertThat(expiryDateResult.getAttribute("title")).isEqualTo(inventoryDetailsFromFile.get(i).getExpiryDate());
					}

					softAssertions.assertAll();
				} else if (isDataNew == false && "Skip".equalsIgnoreCase(this.handlingMethod)) {
					softAssertions.assertThat(inventoryStatusResult.getAttribute("title")).isEqualTo(inventoryDetailsList.get(i).getStatus());
					softAssertions.assertThat(assignedToResult.getAttribute("title")).isEqualTo(inventoryDetailsList.get(i).getAssignedUser());
					softAssertions.assertThat(createdTimeResult.getAttribute("title").contains(inventoryDetailsList.get(i).getCreatedTime()));
					softAssertions.assertThat(modifiedTime.getAttribute("title")).contains(SkyHighUtil.getCurrentDateTime("dd-MM-yyyy"));
					
					if (!ResourceType.MSN.getDescription().equalsIgnoreCase(resourceType)) {
						softAssertions.assertThat(iccidResult.getAttribute("title")).isEqualTo(inventoryDetailsList.get(i).getIccID());
						softAssertions.assertThat(imsiResult.getAttribute("title")).isEqualTo(inventoryDetailsList.get(i).getImsi());
						softAssertions.assertThat(expiryDateResult.getAttribute("title")).isEqualTo(inventoryDetailsList.get(i).getExpiryDate());
						
						if (ResourceType.ESIM.getDescription().equalsIgnoreCase(resourceType)) {
							softAssertions.assertThat(activationCodeResult.getAttribute("title")).isEqualTo(inventoryDetailsList.get(i).getActivationCode());
						}
					}
					softAssertions.assertAll();
				}
				break;
			}
		}
	}

	
	private void searchInventoryData(String inventoryColumn, String searchData) {
		log.info("Search for Inventory Data");

		switch (InventoryColumns.fromDescription(inventoryColumn)) {
		
		case MSN:
			new TextfieldElement(driver, msnTextField).setValue(searchData);
			break;

		case SIM_NUMBER:
			new TextfieldElement(driver, simTextField).setValue(searchData);
			break;

		case ICCID:
			new TextfieldElement(driver, iccIdField).setValue(searchData);
			break;
			
		case VOUCHER_CONTROL_ID:
			new TextfieldElement(driver, voucherControlID).setValue(searchData);
			break;
			
		default:
			Assert.fail("Invalid Inventory Column : " + inventoryColumn);
		}
		
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
		new ButtonElement(driver, inventorySearchButton).click();
		driver.waitForTimeout(10000);
	}
	
	public void submitImportPage() {
	    log.info("Submit Import Page");
		driver.waitForTimeout(2000);
		new ButtonElement(driver, submitImport).click();
		assertThat("Import Summary is not displayed", importSummary.isVisible(), equalTo(true));
	    SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		//new ButtonElement(driver, finishButton).click();
		driver.waitForTimeout(2000);
		assertThat("Successful import is 0", successResult.innerText().trim().equalsIgnoreCase("0"), equalTo(false));
		new ButtonElement(driver, closeButton).click();
		SkyHighUtil.deleteCSVFile(csvFilePath);
	}

	public void enterImportDetails(String resourceType) {
	    log.info("Enter Import Details");

		driver.waitForTimeout(2000);
		new DropdownElement(driver, importAssignedDropdown).select2DropdownDialogBox(importAssignedUserSearch.last(), getUserName());
		driver.waitForTimeout(2000);
		new DropdownElement(driver, carrierDropdown).select2DropdownDialogBox(carrierSearch, CARRIER);
		driver.waitForTimeout(2000);
		
		if (!ResourceType.MSN.getDescription().equalsIgnoreCase(resourceType)) {
			new DropdownElement(driver, starterKitDropdown).select2DropdownDialogBox(starterKitSearch, STARTER_PACK);
			driver.waitForTimeout(2000);
		}
		new ButtonElement(driver, importNextStepButton).click();
	}
	
	public void selectHandlingMethod(String handlingMethod) {
	    log.info("Select Handling Method");
		this.handlingMethod = handlingMethod;
		new DropdownElement(driver, mergeTypeDropdown).select2DropdownDialogBox(mergeTypeSearch.last(), handlingMethod);
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		new ButtonElement(driver, submitDuplicatePage).click();
	}
	
	public void checkDuplicateEntries(String resourceType, String searchColumn) throws CsvValidationException, IOException {
	    log.info("Check Duplicate Entries");
		inventoryDetailsFromFile = SkyHighUtil.extractResourcesFromCSV(resourceType, this.filePath);
		inventoryDetailsList = new ArrayList<InventoryDetails>();
		this.searchColumn = searchColumn;
		
		for (int i = 0; i < inventoryDetailsFromFile.size(); i++) {
			String searchData = getSearchData(inventoryDetailsFromFile, resourceType, i);
			searchInventoryData(searchColumn, searchData);
			InventoryDetails inventoryDetails = new InventoryDetails();
			
			if (!emptyRecordMessage.isVisible()) {
				inventoryDetails.setIsDataNew(false);;
				inventoryDetails.setAssignedUser(assignedToResult.innerText());
				inventoryDetails.setCreatedTime(createdTimeResult.innerText());
				inventoryDetails.setStatus(inventoryStatusResult.innerText());
				log.info("isDataNew: {} Assigned User : {}, Created Time : {}, Status : {}",  inventoryDetails.getIsDataNew(),
						inventoryDetails.getAssignedUser(), inventoryDetails.getCreatedTime(), inventoryDetails.getStatus());
				
				if (!ResourceType.MSN.getDescription().equalsIgnoreCase(resourceType)) {
					inventoryDetails.setSimNumber(simNumberResult.innerText());
					inventoryDetails.setIccID(iccidResult.innerText());
					inventoryDetails.setImsi(imsiResult.innerText());
					inventoryDetails.setExpiryDate(expiryDateResult.innerText());
					log.info("iccID: {} IMCI : {}, ExpiryDate : {}", inventoryDetails.getIccID(), inventoryDetails.getImsi(),
							inventoryDetails.getExpiryDate());
					if (ResourceType.ESIM.getDescription().equalsIgnoreCase(resourceType)) {
						inventoryDetails.setActivationCode(activationCodeResult.innerText());
						log.info("Activation Code {}", inventoryDetails.getActivationCode());
					}
				}
					
			} else {
				inventoryDetails.setIsDataNew(true);
				log.info("isDataNew : {}", inventoryDetails.getIsDataNew());
			}
			
			inventoryDetailsList.add(inventoryDetails);
		}
	}
	
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Map<String, String> getFilenameMap() {
		return filenameMap;
	}


	private List<List<String>> generateResourceList(int count, String resourceType) {
		log.info("Generate Resource List");
		List<List<String>> itemList = new ArrayList<>();
		String prefix = "61";

		for (int i = 0; i < count; i++) {
			 List<String> row = new ArrayList<>(); 
			 
			if (ResourceType.MSN.getDescription().equalsIgnoreCase(resourceType)) {
				row.add(prefix + SkyHighUtil.generateRandomDigits(9));
				itemList.add(row);

			} else {
				row.add("88810140000" + SkyHighUtil.generateRandomDigits(9));
				
				if (ResourceType.PHYSICAL_SIM.getDescription().equalsIgnoreCase(resourceType))
					row.add("40000" + SkyHighUtil.generateRandomDigits(8));
				
				row.add(SkyHighUtil.generateRandomDigits(8)); //puk
				row.add("507777" + SkyHighUtil.generateRandomDigits(9));
				row.add(SkyHighUtil.getFutureDateTime("dd-MM-yyyy", 2, "month"));

				if (ResourceType.ESIM.getDescription().equalsIgnoreCase(resourceType))
					row.add("LPA:1$sm-v4-017-ppa-gtm.pr.go-esim.com$" + SkyHighUtil.generateRandomString(32).toUpperCase());
				itemList.add(row);
			}
		}
		return itemList;
	}
	
	
	private String getSearchData(List<InventoryDetails> list, String resourceType, int index) {
		log.info("Get Data to be search in inventory for each resource type");
		String searchData = null;

		switch (ResourceType.fromDescription(resourceType)) {
		
		case MSN:
			searchData = list.get(index).getMsn();
			break;
			
		case PHYSICAL_SIM:
			searchData = list.get(index).getSimNumber();
			break;
			
		case ESIM:
			searchData = list.get(index).getIccID();
			break;
			
		default:
			Assert.fail("Invalid Resource Type : " + resourceType);
		}
		
		return searchData;
	}

	public String[] getActivationCodeDetails() {
		log.info("Get Activation Code Details");
		String[] activationCodeDetails = {activationCode.innerText(), starterPack.innerText()};
		return activationCodeDetails;
	}
}
