package com.pages;


import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;

import org.junit.Assert;

import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.utils.SkyHighUtil;

public class CsmartBulkOperationPage extends SkyHighPage {
	
    public CsmartBulkOperationPage(Page driver) {
    	super(driver);
    }

    String bulkAction;
    boolean isUploadSuccessful = false;
    Path csvFilePath;
	
	private Locator operationDropdown = driver.locator("#s2id_operation");
	private Locator operationSearch = driver.locator(".select2-drop-active .select2-input");
	private Locator selectFileButton = driver.locator("//*[@id=\"uploadfile\"]");
	private Locator submitButton = driver.locator("//*[@id=\"submit_button\"]");
	private Locator bulkStatusField = driver.locator("//*[@id=\"operationsList\"]/tbody/tr[1]/td[9]");
	
	
	public void selectBulkAction(String bulkAction) {
	    log.info("Select Bulk Action");
		this.bulkAction = bulkAction;
		new DropdownElement(driver, operationDropdown).select2DropdownDialogBox(operationSearch, bulkAction);
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

	public void uploadFile(Map<String, String> map) {
	    log.info("Upload File from Computer");

		String value = null;
		
	    if(map.containsKey(bulkAction)) {
	    	value = map.get(bulkAction);
	    	csvFilePath = Paths.get(System.getProperty("user.home"), value);
	    } else {
	    	Assert.fail("Invalid Bulk Operation : " + bulkAction);
	    }

		if (Files.exists(csvFilePath)) {
			selectFileButton.setInputFiles(csvFilePath);
			isUploadSuccessful = true;
			log.info("File Path: {}", csvFilePath.toAbsolutePath());
			
		} else {
			log.error("File not found: {} ", csvFilePath.toAbsolutePath());
		}
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
		new ButtonElement(driver, submitButton).click();
		SkyHighUtil.deleteCSVFile(csvFilePath);
		driver.waitForTimeout(5000);
	}


	public void validateOperationStatus(String bulkAction, String status) {
		log.info("Validate Operation Status");
		waitForPageToLoad();

			String bulkStatus = driver.locator("//*[@id=\"operationsList\"]/tbody/tr/td[3][contains(.,'" + bulkAction
					+ "')]" + "/following::td[6]").first().innerText();

			if (!"Completed".equalsIgnoreCase(bulkStatus)) {
				for (int minute = 1; minute <= 6; minute++) {
					driver.reload();
					waitForPageToLoad();
					bulkStatus = bulkStatusField.innerText();
					if ("Completed".equalsIgnoreCase(bulkStatus)) {
						break;
					}
					log.info("Current Order status: {}, Refresh: {} min", bulkStatus, minute);
					driver.waitForTimeout(60000);
				}
			} else {
				log.info("Current Order status: {}", bulkStatus);
			}

			SkyHighUtil.takeScreenshot(driver, new Object() {
			}.getClass().getEnclosingMethod().getName());
			assertThat("On Operation Status", bulkStatus, equalTo("Completed"));

	}

}
