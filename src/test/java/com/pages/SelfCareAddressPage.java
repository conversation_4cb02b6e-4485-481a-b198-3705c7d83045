package com.pages;

import com.customer.AddressDetails;
import com.elements.ButtonElement;
import com.elements.TextfieldElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;
import com.utils.SkyHighUtil;

public class SelfCareAddressPage extends SkyHighPage  {
	
	public SelfCareAddressPage(Page driver) {
		super(driver);
	}
	
	private Locator addressSearchField = driver.locator("//*[@name='address']");
	private Locator nextButton =  driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Next"));
	
	
	public void enterAddressDetails(AddressDetails addressDetails) {
		log.info("Enter Customer Address Details");
		new TextfieldElement(driver, addressSearchField).setValue(addressDetails.getAddress());
		driver.waitForTimeout(2000);
		Locator addressResult= driver.locator("//span[@class='_highlight_1h5p5_116'][contains(text(),'" + addressDetails.getAddress()+ "')]");
		new ButtonElement(driver, addressResult).click();
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
		
	}

	public void submitAddressPage() {
		log.info("Submit Address Page");
		new ButtonElement(driver, nextButton).click();
		
	}

}
