package com.pages;

import java.io.IOException;

import org.junit.Assert;

import com.customer.CustomerDetails;
import com.customer.ImportDetails;
import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.elements.TextfieldElement;
import com.enums.ActivationType;
import com.enums.CustomerType;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;
import com.utils.SkyHighUtil;

public class SelfCareActivationPage extends SkyHighPage  {
	
	private Locator activateButton =  driver.getByRole(AriaRole.BANNER).getByRole(AriaRole.LINK, new Locator.GetByRoleOptions().setName("Activate"));
	private Locator simRadioButton = driver.locator("//*[@class='_customRadio_1bxv5_22']");
	private Locator activationCodeField = driver.locator("//*[@id=\"activationCode\"]");
	private Locator validateButton = driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Validate"));
	private Locator continueButton =  driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Continue with physical SIM"));
	private Locator submitSimPageButton =  driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Get started"));
	private Locator activationCodeErrorText = driver.locator("//*[@id=\"root\"]/div[1]/div/p");
	private Locator serviceNumberDropdown =  driver.locator("div._dropdown_lr0wu_1").nth(0);
	private Locator serviceNumberSelection =  driver.locator("div._option_lr0wu_14").nth(0);
	private Locator nextButton = driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Next"));
	private Locator submitActivationPage =  driver.locator("//div[@class='_buttonName_1n0jw_171']");
	private Locator portRadioButton =  driver.locator("//div[@class='_buttonName_1n0jw_171']");

	public SelfCareActivationPage(Page driver) {
		super(driver);
	}
	
	public void initiateActivation() {
		log.info("Initiate Activation");
		new ButtonElement(driver, activateButton).click();
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

	public void selectSimType(String simType) {
		log.info("Select Sim Type: {}", simType);
		driver.waitForTimeout(2000);
		Locator selectedSim = simType.equalsIgnoreCase("Physical SIM") ? simRadioButton.first() : simRadioButton.last();
		new ButtonElement(driver, selectedSim).click();
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}
	
	public void submitSimTypePage() {
		log.info("Submit Activation Code Page");
		new ButtonElement(driver, submitSimPageButton).click();
	}

	public void enterActivationCode(String activationCode) {
		log.info("Enter Activation Code");
		new TextfieldElement(driver, activationCodeField).setValue(activationCode);
		new ButtonElement(driver, validateButton).click();
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}
	
	public void verifyActivationCodePage() {
		log.info("Verify if Activation Code is Valid");
		driver.waitForTimeout(4000);
		if (activationCodeErrorText.isVisible()) 
			Assert.fail("Activation Code is Invalid");
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

	public void submitActivationCodePage() {
		log.info("Submit Activation Code Page");
		new ButtonElement(driver, continueButton).click();
		
	}

	public CustomerDetails getCustomerDetails(String dataFile) throws StreamReadException, DatabindException, IOException {
		log.info("Get Customer Details from file");
		String filePath = getClass().getClassLoader().getResource(dataFile).getPath();
		CustomerDetails customerDetails = (CustomerDetails) SkyHighUtil.readDetailsFromFile(filePath, CustomerDetails.class);
		return customerDetails;
	}

	public void selectNumberType(String activationType) {
		log.info("Select Number Type");
		if (!ActivationType.NEW.getDescription().equalsIgnoreCase(activationType)) 
			new ButtonElement(driver, portRadioButton).click();
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}
	
	public void submitNumberPage() {
		new ButtonElement(driver, nextButton).click();
		
	}

	public void enterPortingDetails(ImportDetails importDetails, String testDataFile) {
		// TODO Auto-generated method stub
		
	}

	public int getTestDataIndex(String customerType) {
	int testDataIndex = 0;
		if (CustomerType.EXISTING_CUSTOMER.getDescription().equalsIgnoreCase(customerType))
			testDataIndex = 1;
		return testDataIndex;
	}

	public void selectServiceNumber() {
		log.info("Select Service Number");
		new DropdownElement(driver, serviceNumberDropdown).click();
		new ButtonElement(driver, serviceNumberSelection).click();
	}

	public void submitActivationPage() {
		new ButtonElement(driver, submitActivationPage).click();
		
	}

	public void performVerification() {
		// TODO Auto-generated method stub
		
	}
}
