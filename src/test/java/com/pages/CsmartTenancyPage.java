package com.pages;

import com.elements.ButtonElement;
import com.elements.TextfieldElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.utils.SkyHighUtil;

public class CsmartTenancyPage extends SkyHighPage {
	
    public CsmartTenancyPage(Page driver) {
    	super(driver);
    }
    
	private Locator tenantSearchField = driver.locator("//*[@name='rolename']");
	private Locator searchButton = driver.locator(".table-actions > .btn");
	private Locator tenantsResult = driver.locator("//*[@id=\"Tenants_listView_row_1\"]/td[2]");
	private Locator detailsTab = driver.locator("//strong[contains(.,'Details')]");
	private Locator editButton = driver.locator("//*[@id=\"Tenants_detailView_basicAction_LBL_EDIT\"]");
	private Locator csvHeaderCheckbox = driver.locator("//*[@id=\"Tenants_editView_fieldName_is_header\"]");
	private Locator saveButton = driver.locator("//button[@type='submit']");

	
	public void validateCSVHeader(String csvHeader, String tenant) {
		new TextfieldElement(driver, tenantSearchField).setValue(tenant);
		new ButtonElement(driver,searchButton).click();
		driver.waitForTimeout(5000);
		new ButtonElement(driver,tenantsResult).click();
		new ButtonElement(driver,detailsTab).click();
		new ButtonElement(driver,editButton).click();
		driver.waitForTimeout(2000);
		boolean isChecked = csvHeaderCheckbox.isChecked();
		 
			if (!isChecked) {
				new ButtonElement(driver, csvHeaderCheckbox).click();
				new ButtonElement(driver, saveButton).click();
			}

		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

}
