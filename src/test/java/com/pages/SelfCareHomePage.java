package com.pages;


import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;

import com.elements.ButtonElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.stepDefinition.GlobalVariables;
import com.utils.SkyHighUtil;

public class SelfCareHomePage extends SkyHighPage {
	
	private Locator dashboardMenu =  driver.locator("//*[@id=\"root\"]/div[2]/main/div/div[1]/div/div/a[1]/span");
	private Locator serviceNumberDropdown =  driver.locator("//span[@class='_yourServiceWebCard_1ttz4_111']//div//i[@class='_iconRoot_147s5_1 "
			+ "aldi-mobile-chevron-down _theme-inherit_147s5_23']");
	private Locator accounNumberText =  driver.locator("//div[@class='_infoContainer_1o914_18']/p");
	
	private String selfcareMenu;
    public SelfCareHomePage(Page driver) {
    	super(driver);
    }
    
	public void validateHomePage() {
		log.info("Validate Home Page in Self Care");
		driver.waitForTimeout(4000);
		assertThat("Dashboard Menu is not displayed", dashboardMenu.isVisible(), equalTo(true));
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public void setSelfcareMenu(String selfcareMenu) {
		this.selfcareMenu = selfcareMenu;
		
	}

	public void selectSelfcareMenu() {
		log.info("Select Module from Self Care Menu");
		super.waitForPageToLoad();
		driver.waitForTimeout(4000);
		Locator managAccountElement = driver.locator("//span[contains(.,'" + selfcareMenu + "')]").last();
		new ButtonElement(driver, managAccountElement).click();
		log.info("{} option is clicked", managAccountElement);
	}
	
	public void selectServiceNumber(String serviceStatus, String serviceNumber) {
		log.info("Select Service Number");
		if (StringUtils.isNotEmpty(serviceNumber)) { 
			super.waitForPageToLoad();
			new ButtonElement(driver, serviceNumberDropdown).click();
			Locator serviceNumberOption = driver.locator("//div[contains(@class, '_option') and (contains(@class, '_option_lr0wu'))]"
					+ "//p[contains(@class, '_value') and normalize-space()='" + serviceNumber + "']");
			new ButtonElement(driver, serviceNumberOption).click();
		} else {
			Assert.fail("Please provide service number");
		}
		
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public void verifyAccountNUmberDisplay() {
		log.info("Verify Account Number Displayed");
		assertThat("Account Number is not displayed", accounNumberText.innerText().contains(GlobalVariables.getAccountNumber()));
		
	}

}