package com.pages;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import java.awt.datatransfer.UnsupportedFlavorException;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.junit.Assert;

import com.elements.ButtonElement;
import com.file.WriteToFile;
import com.microsoft.playwright.Download;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.utils.SkyHighUtil;

public class SelfCareInvoicePage extends SkyHighPage {
	
	protected Locator downloadPDFButton = driver.locator("//*[@id=\"invoices\"]/tbody/tr[1]/td[4]/a");
	protected Locator totalCostDisplay = driver.locator("//*[@id=\"invoices\"]/tbody/tr[1]/td[3]");
	String invoiceNumber;
	String statement_url;

	public SelfCareInvoicePage(Page driver) {
		super(driver);

	}

	public void downloadPDFStatement() {
		log.debug("Download PDF Statement");
		//invoiceNumber = UniteInvoicePage.getInvoiceNumber();
		Locator invoiceElement = driver.locator("//td[contains(.,'" + invoiceNumber + "')]/following::td[3]/a");
		Path downloadPath = Paths.get(System.getProperty("user.home").replace("\\", "/") + "/Downloads");
		
		Download download = driver.waitForDownload(() -> {
			new ButtonElement(driver, invoiceElement).click();
		});
		
		//statement_url = downloadPath.toString() + "/invoice-" + UniteInvoicePage.getInvoiceNumber() + ".pdf";
		download.saveAs(Paths.get(statement_url));
		
		driver.waitForTimeout(30000);

	}

	public String openDownloadedPDFStatement() throws Throwable, UnsupportedFlavorException, IOException {
		log.debug("PDF Invoice URL location: " + statement_url);
		driver.navigate(statement_url);
		return readPDFContent();
	}

	public String readPDFContent() throws Exception {
		log.debug("Read PDF Content");
		PDDocument document = null;
		String output = null;
		
		try {
			File file = new File(statement_url);
			document = Loader.loadPDF(file);
			log.info("---Printing PDF contents---");
			output = new PDFTextStripper().getText(document);
			SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		} finally {
			if (document != null) {
				document.close();
			}
		}
		log.info("PDF Contents: {}", output);
		return output;
	}

	public void comparePDFValues(String pdfContent, String file) throws IOException {
		log.debug("Compare PDF Content");
		FileInputStream fstream = new FileInputStream(WriteToFile.getFilePath());
		BufferedReader br = new BufferedReader(new InputStreamReader(fstream));
		String data;

		// Read File Line By Line
		while ((data = br.readLine()) != null) {
			try {
				Assert.assertTrue(pdfContent.contains(data));
				log.info("Value {} exists in actual PDF. PASSED!", data);
			} catch (AssertionError e) {
				Assert.fail("Expected value '" + data + "' not in actual generated PDF! FAILED!");
			}
		}
		
		// Close the input stream
		fstream.close();

		Path pdf_path = FileSystems.getDefault().getPath(statement_url);
		log.info("Cleanup: Delete statement pdf file" + pdf_path);
		deleteFileFromSystem(pdf_path);
		
		log.info("Cleanup: Delete billrun data file");
		Path path = FileSystems.getDefault().getPath(WriteToFile.filePath.get());
		deleteFileFromSystem(path);
	}

	private void deleteFileFromSystem(Path path) throws IOException {
		  try {
	            Files.delete(path);
	        } catch (NoSuchFileException x) {
	            log.error("%s: no such" + " file or directory%n", path);
	        } 
		}

	public String computeGST(String totalCost) {
		log.debug("Compute for GST");
		String total = StringUtils.substringAfter(totalCost, "$");
		return SkyHighUtil.convertDecimalPlaces(Double.valueOf(total) / 11f, 2);
	}

	public void validateTotalCostDisplay(String totalCost) {
		log.debug("Validate Total Cost Display");
		assertThat("Paypal Email does not match", totalCostDisplay.innerText(),equalTo(totalCost));
		
	}
}
