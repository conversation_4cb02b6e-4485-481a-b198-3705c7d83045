	package com.pages;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import java.util.ArrayList;
import java.util.List;

import org.assertj.core.api.SoftAssertions;

import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.elements.TextfieldElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.utils.SkyHighUtil;

public class CsmartRechargeVoucherPage extends CsmartInventoryPage {

	public CsmartRechargeVoucherPage(Page driver) {
		super(driver);

	}
	
	List<String> inventoryList = new ArrayList<String>();
	List<String> resourceList;
	String handlingMethod;
	String ticketNo;
	private final static String INTERNAL_MEMO= "This is for Regression Testing";

	private Locator moreButton = driver.locator("//*[@id=\"listview-actions\"]/div/div[1]/div/div/button");
	private Locator searchButton = driver.locator("//*[@id=\"listview-table\"]/thead/tr[2]/th[1]/div/button");
	private Locator emptyRecordMessage = driver.locator("//*[@id=\"listview-table\"]/tbody/tr/td/div/text()[1]");
	private Locator listViewContent = driver.locator("//*[@id=\"listViewContent\"]");
	
	//GeneratePIN
	private Locator generateButton = driver.locator("//strong[contains(.,'Generate')]");
	private Locator voucherRequestOption = driver.locator("//*[@id=\"RechargeVoucher_listView_advancedAction_Voucher_Requests\"]");
	private Locator generateVoucherOption = driver.locator("//*[@id=\"RechargeVoucher_listView_advancedAction_Generate_Vouchers\"]");
	private Locator voucherTicketNoField = driver.locator("//*[@name='ticket_no']");
	private Locator ticketSearchButton =  driver.locator("//td/button");
	private Locator assignedUserDropdown = driver.locator("#s2id_userId1");	
	private Locator assignedUserSearch = driver.locator(".select2-drop-active .select2-input");
	private Locator inventoryTicketNoField = driver.locator("//*[@name='voucherticket']");
	private Locator internalMemoField = driver.locator("//*[@name='internalmemo']");
	private Locator saveButton = driver.locator("//*[@name='saveButton']").first();
	private Locator packageReference = driver.locator("//*[@id=\"RechargeVoucher_listView_row_1\"]/*[@data-name='voucherpackage']");
	private Locator voucherCode = driver.locator("//*[@id=\"RechargeVoucher_listView_row_1\"]/*[@data-name='vouchercode']");
	private Locator voucherCodeSearchField = driver.locator("//*[@name='voucherpin']");
	private Locator soldButton = driver.locator("//*[@id=\"RechargeVoucher_detailView_basicAction_Sold\"]");
	

	public void generateVoucherPIN(String ticketNo, String user) {
	    log.info("Generate Voucher PIN");
	    this.ticketNo = ticketNo;
		new ButtonElement(driver, moreButton).click();
		new ButtonElement(driver, generateVoucherOption).click();
		searchTicketNo();
		new DropdownElement(driver, assignedUserDropdown).select2DropdownDialogBox(assignedUserSearch, user);
	    SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		new ButtonElement(driver, generateButton).click();
		driver.waitForTimeout(5000);
	}

	public void validateGeneratedVoucher(String status, String ticketNo, int voucherCount, String user) {
	    log.info("Validate Generated Voucher");
	    driver.waitForCondition(() -> listViewContent.isVisible());
		new TextfieldElement(driver, inventoryTicketNoField).setValue(ticketNo);
		new ButtonElement(driver, searchButton).click();
		assertThat("No result is found", emptyRecordMessage.isVisible(), equalTo(false));
		driver.waitForTimeout(5000);
		List<Locator> actualVoucherCount = driver.locator("//td[5]/span/span[contains(.,'" + ticketNo + "')]").all();
		assertThat("Voucher Count Does not Match", voucherCount, equalTo(actualVoucherCount.size()));
				
	    for (int i = 0; i < voucherCount; i++) {
	    	Locator voucherSearchResult = driver.locator("//*[@id=\"RechargeVoucher_listView_row_" + (i + 1) + "\"]/td[6]");
	    	Locator assignedSearchResult = driver.locator("//*[@id=\"RechargeVoucher_listView_row_" + ( i + 1) + "\"]/td[15]");
	    	Locator createdTimeResult = driver.locator("//*[@id=\"RechargeVoucher_listView_row_" + (i + 1) + "\"]/td[10]");

			SoftAssertions softAssertions = new SoftAssertions();
			softAssertions.assertThat(voucherSearchResult.innerText()).isEqualTo(status);
			softAssertions.assertThat(assignedSearchResult.innerText()).isEqualTo(user);
			softAssertions.assertThat(createdTimeResult.innerText()).contains(SkyHighUtil.getCurrentDateTime("dd-MM-yyyy"));
			softAssertions.assertAll();
	    }

		log.info("Ticket Voucher Count: {}, Voucher Count Created: {}", voucherCount, actualVoucherCount.size());
	    SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		
	}

	public void evaluateRequest(String bypassApproval, String ticketNo, String approvalStatus) {
	    log.info("Approve or Reject Voucher Request");
	    if ("No".equalsIgnoreCase(bypassApproval)) {
			this.ticketNo = ticketNo;
			new ButtonElement(driver, moreButton).click();
			new ButtonElement(driver, voucherRequestOption).click();
			searchTicketNo();
			driver.waitForTimeout(5000);
			Locator actionLocator = driver.locator("//input[@id='" + approvalStatus.toLowerCase() + "']");
			new ButtonElement(driver, actionLocator).click();
			new TextfieldElement(driver, internalMemoField).setValue(approvalStatus + ":" + INTERNAL_MEMO);
			new ButtonElement(driver, saveButton).click();
			driver.waitForTimeout(5000);
	    } else {
	    	log.info("Nothing to approve. Finance approval is bypassed");
	    }
	    
	}
	
	public void searchTicketNo() {
	    log.info("Search for Ticket No");
		new TextfieldElement(driver, voucherTicketNoField).setValue(this.ticketNo);
		new ButtonElement(driver, ticketSearchButton).click();
		Locator searchResult = driver.locator("//*[@id=\"HelpDesk_popUpListView_row_1\"]/td[3][contains(.,'" + this.ticketNo + "')]");
		new ButtonElement(driver, searchResult).click();
		driver.waitForTimeout(5000);
	}

	public String[] getVoucherCodeDetails() {
		log.info("Get Voucher Code Details");
		String[] voucherCodeDetails = {packageReference.innerText().trim(), voucherCode.innerText().trim() };
		new ButtonElement(driver, voucherCode).click();
		new ButtonElement(driver, soldButton).click();
		log.info("Voucher Code" + voucherCodeDetails[1]);
		return voucherCodeDetails;

	}
}
