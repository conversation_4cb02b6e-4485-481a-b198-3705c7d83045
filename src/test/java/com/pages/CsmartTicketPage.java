package com.pages;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.elements.TextfieldElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.utils.SkyHighUtil;

public class CsmartTicketPage extends SkyHighPage {
	

	private final static String VOUCHER= "Request for Vouchers";
	private String batchName;
	private String ticketNo;
	private Locator addTicketButton = driver.locator("//*[@id=\"HelpDesk_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator titleField = driver.locator("//*[@id=\"HelpDesk_editView_fieldName_ticket_title\"]");
	private Locator ticketCategoryDropdown = driver.locator("#s2id_autogen1");
	private Locator ticketCategorySearch = driver.locator("#s2id_autogen2_search");
	private Locator addVoucherInfoButton = driver.locator("//*[@id=\"addLineItem\"]");
	private Locator packageIcon = driver.locator("(//img[@title='Packages'])[3]");
	private Locator voucherStatusDropdown = driver.locator("#s2id_autogen38");
	private Locator voucherStatusSearch = driver.locator("#s2id_autogen39_search");
	private Locator packageName = driver.locator("//*[@id=\"Packages_popUpListView_row_1\"]/td[3]/a");
	private Locator totalQuantityTextField = driver.locator("//*[@id=\"quantity1\"]");
	private Locator batchNameTextField = driver.locator("//*[@id=\"batchname1\"]");
	private Locator datePicker = driver.locator("//div[@class=\"datepicker-days\"]/table/tbody/tr/td[@class=\"day\"]");
	private Locator issueDateField = driver.locator("#issuedate1");
	private Locator expiryDateField = driver.locator("#expirydate1");
	private Locator saveButton = driver.locator("//*[@id=\"EditView\"]/div[3]/div/div/button");
	private Locator summaryTab = driver.locator("//strong[contains(.,'Summary')]");
	private Locator ticketStatusField = driver.locator("//form[@id='detailView']/div/div/div[2]/div/table/tbody/tr[3]/td[2]/div/span/span");
	private Locator bypassApproval = driver.locator("//*[@id=\"HelpDesk_editView_fieldName_bypass_approval\"]");
	
    public CsmartTicketPage(Page driver) {
    	super(driver);
    }

	public void enterTicketDetails(int voucherCount, String voucherStatus) {
		log.info("Enter Ticket Details");
		new ButtonElement(driver, addTicketButton).click();
		new TextfieldElement(driver, titleField).setValue("TEST" + SkyHighUtil.generateRandomNumber(100000));
		new DropdownElement(driver, ticketCategoryDropdown).select2DropdownSelect(ticketCategorySearch, VOUCHER);
		new DropdownElement(driver, voucherStatusDropdown).select2DropdownSelect(voucherStatusSearch, voucherStatus);
		new ButtonElement(driver, addVoucherInfoButton).click();
		new ButtonElement(driver, packageIcon).click();
		new ButtonElement(driver, packageName).click();
		new TextfieldElement(driver, totalQuantityTextField).setValue(Integer.toString(voucherCount));

		batchName = "BATCH-" + SkyHighUtil.generateRandomNumber(1000);
		new TextfieldElement(driver, batchNameTextField).setValue(batchName);
		new ButtonElement(driver, issueDateField).click();
		new ButtonElement(driver, datePicker.first()).click();
		new ButtonElement(driver, expiryDateField).click();
		new ButtonElement(driver, datePicker.last()).click();
		driver.waitForTimeout(2000);
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public void submitTicket() {
		log.info("Submit Ticket");
		new ButtonElement(driver, saveButton).click();
		driver.waitForCondition(() -> summaryTab.isVisible());
	}

	public void validateStatus(String bypassApprovalStatus) {
		log.info("Validate Ticket Status");
		String status = "Approved";
		
		if ("No".contentEquals(bypassApprovalStatus)) 
			status = "Request for Finance Approval";
		
		ticketNo = driver.locator("//label[contains(.,'Ticket Number')]/following::td[1]/div/span").innerText();
		driver.waitForTimeout(10000);
		assertThat("Ticket is not in Approved Status", ticketStatusField.innerText().trim(), equalTo(status));
		log.info("Ticket No is {} and Ticket Status is {} ", ticketNo, ticketStatusField.innerText());
	}

	public String getTicketNo() {
		log.info("Get Ticket Number");
		return ticketNo;
	}

	public void bypassApproval(String bypassApprovalStatus) {
		log.info("Bypass Approval");
		if ("Yes".equalsIgnoreCase(bypassApprovalStatus) && bypassApproval.isChecked() == false) {
				new ButtonElement(driver, bypassApproval).click();
		} else if ("No".equalsIgnoreCase(bypassApprovalStatus) && bypassApproval.isChecked() == true) {
				new ButtonElement(driver, bypassApproval).click();
		}
	}

	public String getBatchName() {
		return batchName;
	}

	public void setBatchName(String batchName) {
		this.batchName = batchName;
	}
}
