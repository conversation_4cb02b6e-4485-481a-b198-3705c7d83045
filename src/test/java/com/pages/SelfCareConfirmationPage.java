package com.pages;

import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.stepDefinition.GlobalVariables;
import com.utils.SkyHighUtil;

public class SelfCareConfirmationPage extends SkyHighPage  {
	
	private Locator agreeTermsCheckBox = driver.locator("//form[@class='_formContainer_1n0jw_130']//span[@class='_customCheckbox_1o4hk_32']");
	private Locator firstOptionSelected =  driver.locator("div._option_lr0wu_14").nth(0);
	private Locator whereDoYouHearDropdown = driver.locator("//span[normalize-space()='Where did you hear of us?']/following::div[1]");
	private Locator serviceNumberText = driver.locator("//p[normalize-space()='New Number']/following::p[1]");
	
	public SelfCareConfirmationPage(Page driver) {
		super(driver);
	}

	public void reviewOrder() {
		log.info("User review and confirm order");
		new DropdownElement(driver, whereDoYouHearDropdown).click();
		new ButtonElement(driver, firstOptionSelected).click();
		new ButtonElement(driver, agreeTermsCheckBox).click();
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public void getServiceNumber() {
		log.info("Get Service Number");
		GlobalVariables.setServiceNumber(serviceNumberText.innerText().trim().replace(" ", ""));
		log.info("Service Number is {}", GlobalVariables.getServiceNumber());
	}

}