package com.pages;


import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.elements.TextElement;
import com.stepDefinition.GlobalVariables;
import com.utils.SkyHighUtil;

public class SelfCareSubmissionPage extends SkyHighPage {

	private static ThreadLocal<String> orderNumber = new ThreadLocal<String>();
	
	private Locator orderIDText = driver.locator("//h5[normalize-space()='Order ID']/following::p[1]");
	private Locator accountNumberText = driver.locator("//h5[@class='_heading_h1web_211']/following::p[1]");
	private Locator activationThankYouMessage = driver.locator("//*[@id=\"root\"]/div[2]/main/div/div/h2");

	public SelfCareSubmissionPage(Page driver) {
		super(driver);
	}

	private final static String THANK_YOU_MESSAGE= "Thanks for choosing ALDI Mobile";
	
	public void verifyOrderCreated(String orderType, String planName) {
		log.info("Verify if Order Number is created");
		waitForPageToLoad();
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		SelfCareSubmissionPage.setOrderNumber(orderIDText.innerText().trim());
	}
	
	public static String getOrderNumber() {
		return SelfCareSubmissionPage.orderNumber.get();
	}
	
	public static void setOrderNumber(String orderNumber) {
		SelfCareSubmissionPage.orderNumber.set(orderNumber);
	}

	public void verifyAccountNumberCreated() {
		log.info("Get Account Number");
		String accountNumber = new TextElement(driver, accountNumberText).getText().trim();
		GlobalVariables.setAccountNumber(accountNumber);
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		log.info("Account Number: {}", GlobalVariables.getAccountNumber());
		
	}
	
	public void verifyOrderCreated() {
		log.info("Verify if Order Number is created in Order Page");
		driver.waitForCondition(() -> orderIDText.isVisible());
		SelfCareSubmissionPage.setOrderNumber(orderIDText.innerText().trim());
		log.info("Order ID: {}", SelfCareSubmissionPage.getOrderNumber());
	}
	
	public void verifyThankYouMessage() {
		log.info("Verify that thank you message is displayed in Submission Page");
		driver.waitForCondition(() -> activationThankYouMessage.isVisible());
		assertThat("Thank you message is not displayed", activationThankYouMessage.innerText().trim(), equalTo(THANK_YOU_MESSAGE));
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

}
