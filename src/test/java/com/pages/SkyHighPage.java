package com.pages;


import com.elements.ButtonElement;
import com.init.LogInitializer;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Locator.ClickOptions;
import com.microsoft.playwright.Locator.HoverOptions;
import com.utils.SkyHighUtil;

import org.slf4j.Logger;

public class SkyHighPage {

	protected Page driver;
	private Locator blockOverlay;
	private Locator blockPage;
	protected Logger log = LogInitializer.getLogger(SkyHighPage.class);
	private Locator csmartHamburgerMenu;
	private Locator dashboardMenu;
	private Locator customerManagement;
	
    HoverOptions hOptions = new HoverOptions();
    ClickOptions cOptions = new ClickOptions();
	
	public SkyHighPage(Page driver) {
		this.driver = driver;
		this.blockOverlay = driver.locator("//div[contains(@class, 'blockUI blockOverlay')]");
		this.blockPage = driver.locator("//div[contains(@class, 'blockUI blockMsg blockPage')]");
		this.csmartHamburgerMenu = driver.locator("//div[@id='appnavigator']/div/span");
		this.dashboardMenu = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div[1]/div/span[2]");
		this.customerManagement = driver.locator("//*[@id=\"Customers Management_modules_dropdownMenu\"]");
	}

	public void waitForPageToLoad() {
		driver.waitForCondition(() -> blockOverlay.isHidden());
		driver.waitForCondition(() -> blockPage.isHidden());
	}
	
    public void waitForElementLoad(int timeInSeconds) {
        try {
            driver.wait(timeInSeconds);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
	
	public String getWindowsHandle() { 
		String currentWindow = driver.context().pages().toString();
		log.info("Windows Octane " + currentWindow);
		return currentWindow;
	}
	

	public void selectDashboardOption(String csmartOption) {
		log.info("Select {} from Csmart Dashboard Option", csmartOption);
		
		driver.waitForLoadState();
        hOptions.setForce(true);
        hOptions.setTimeout(2000);
        csmartHamburgerMenu.hover(hOptions);
        csmartHamburgerMenu.click(cOptions);
        
		if ("Dashboard".equalsIgnoreCase(csmartOption)) {
			new ButtonElement(driver, dashboardMenu).click();
		} else if ("Customers Management".equalsIgnoreCase(csmartOption)) {
			driver.waitForTimeout(5000);
			customerManagement.hover(hOptions);
		} else {
			Locator csmartMenuOption = driver.getByText(csmartOption, new Page.GetByTextOptions().setExact(true));
			driver.waitForCondition(() -> csmartMenuOption.isVisible());
			driver.waitForTimeout(5000);
			csmartMenuOption.hover(hOptions);
		}

		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}
	public void selectSubMenu(String submenu) {
		log.info("Select {} Submenu From Csmart Dashboard Options", submenu);
		Locator subMenuOption = driver.locator("//*[@id=\"app-menu\"]/div/div[2]/div/ul/div/div/li/a[contains(.,'" + submenu + "')]");
		new ButtonElement(driver, subMenuOption).click();
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		driver.waitForLoadState();
	}

}
