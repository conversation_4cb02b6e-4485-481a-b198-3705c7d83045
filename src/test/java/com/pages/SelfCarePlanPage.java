package com.pages;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import org.junit.Assert;

import com.elements.ButtonElement;
import com.elements.TextElement;
import com.elements.TextfieldElement;
import com.enums.PlanSelection;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;

public class SelfCarePlanPage extends SkyHighPage  {
	
	public SelfCarePlanPage(Page driver) {
		super(driver);
	}

	private Locator nextButton =  driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Next"));
	private Locator amountText =  driver.locator("//h6[contains(.,'Amount Payable')]/following::p[1]");
	private Locator voucherTextField =  driver.locator("//input[@placeholder='Enter voucher code']");
	private Locator applyVoucherButton =  driver.locator("//span[@class='_buttonTitle_2bfnp_35']");
	private Locator planSummarySection =  driver.locator("//h6[@class='_voucherTitle_2bfnp_94']");

	public void selectPlanOption(String planType) {
	    log.info("Select Plan Type");
	   // Locator planTypeLocator = driver.locator("//h6[(text())='" + planType + "']/ancestor::div[contains(@class, '_accordionHeader')]/label/span");
		Locator planTypeLocator = driver.locator("//h6[normalize-space()='" + planType + "']");
		new ButtonElement(driver, planTypeLocator).click();
	}

	public void selectPlanName(String planName) {
		log.info("Select Plan Name");
		Locator planNameLocator = driver.locator("//h5[normalize-space()='" + planName + "']//following::div[1]/button");
		new ButtonElement(driver, planNameLocator).click();
	}

	public void submitPlanPage() {
		log.info("Submit Plan Page");
		new ButtonElement(driver, nextButton).click();
	}

	public void selectPlanType(String planType) {
		log.info("Select Plan Type");
		switch (PlanSelection.fromDescription(planType)) {

		case MOBILE_PLAN:
		case DATA_PLAN:
			Locator planTypeLocator = driver.locator("//span[normalize-space()='" + planType + "']");
			new ButtonElement(driver, planTypeLocator).click();
			break;
			
		case FAMILY_PLAN_USER:
		case FAMILY_PLAN_OWNER:
			
			Locator familyPlanLocator = driver.locator("//h6[normalize-space()='" + planType + "']");
			new ButtonElement(driver, familyPlanLocator).click();
			break;

		default:
			Assert.fail("Invalid plan selection : " + planType);
		}
	}

	public String getAmountPayable() {
		log.info("Get Amount Payable");
		String amountPayable = new TextElement(driver, amountText).getText();
		log.info("Amount Payable is {}", amountPayable);
		return amountPayable;
	}

	public void enterVoucherCode(String voucherCode) {
		log.info("Enter Voucher Code");
		new TextfieldElement(driver, voucherTextField).setValue(voucherCode);
	}

	public void validateVoucherCode() {
		log.info("Validate Voucher Code");
		new ButtonElement(driver, applyVoucherButton).click();
		assertThat("Voucher Code is not Valid", planSummarySection.isVisible(), equalTo(true));
		
	}

	public void setAutoRechargeStatus(String autoRechargeStatus) {
		// TODO Auto-generated method stub
		
	}
}