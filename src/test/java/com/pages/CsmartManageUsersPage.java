package com.pages;


import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import java.io.IOException;

import org.apache.commons.lang3.StringUtils;

import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.elements.TextfieldElement;

import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.user.AddressDetails;
import com.user.CurrencyConfiguration;
import com.user.MoreInformationDetails;
import com.user.UserDetails;
import com.user.UserLoginRole;
import com.utils.SkyHighUtil;


public class CsmartManageUsersPage extends SkyHighPage {
	
	UserDetails userDetails;
	String userName;
	String emailAddress;
	
	public CsmartManageUsersPage(Page driver) {
		super(driver);

	}
	private Locator addUserButton = driver.locator("//*[@id=\"Users_listView_basicAction_LBL_ADD_RECORD\"]");
	private Locator userNameField = driver.locator("//*[@id=\"Users_editView_fieldName_user_name\"]");
	private Locator firstNameField = driver.locator("//*[@id=\"Users_editView_fieldName_first_name\"]");
	private Locator lastNameField  = driver.locator("//*[@id=\"Users_editView_fieldName_last_name\"]");
	private Locator emailAddressField = driver.locator("//*[@id=\"Users_editView_fieldName_email1\"]");
	private Locator passwordField = driver.locator("//*[@id=\"Users_editView_fieldName_user_password\"]");
	private Locator confirmPasswordField = driver.locator("//*[@id=\"Users_editView_fieldName_confirm_password\"]");
	private Locator businessRegionDropdownField = driver.locator("#s2id_Users_Edit_fieldName_business_location");
	private Locator businessRegionSearchField = driver.locator("#s2id_Users_Edit_fieldName_business_location .select2-input");
	private Locator saveUserInformationButton = driver.locator("//*[@id=\"EditView\"]/div[3]/div/div/button");
	private Locator roleDropdownField = driver.locator("#s2id_autogen1");
	private Locator roleSearchField = driver.locator("#s2id_autogen2_search");
	
	private Locator currencyDropdownField = driver.locator("#s2id_autogen10");
	private Locator currencySearchField = driver.locator("#s2id_autogen11_search");
	private Locator truncateTrailingZero = driver.locator("//*[@id=\"Users_editView_fieldName_truncate_trailing_zeros\"]");
	private Locator digitGroupingPattern = driver.locator("#s2id_autogen12");
	private Locator digitGroupingPatternSearch = driver.locator("#s2id_autogen13_search");
	private Locator decimalSeperator = driver.locator("#s2id_autogen14");
	private Locator decimalSeperatorSearch = driver.locator("#s2id_autogen15_search");
	private Locator symbolPlacement = driver.locator("#s2id_autogen18");
	private Locator symbolPlacementSearch = driver.locator("#s2id_autogen19_search");
	private Locator digitGroupingSeperator = driver.locator("#s2id_autogen16");
	private Locator digitGroupingSeperatorSearch = driver.locator("#s2id_autogen17_search");
	
	private Locator titleTextField = driver.locator("//*[@id=\"Users_editView_fieldName_title\"]");
	private Locator departmentTextField = driver.locator("//*[@id=\"Users_editView_fieldName_department\"]");
	private Locator faxTextField = driver.locator("//*[@id=\"Users_editView_fieldName_phone_fax\"]");
	private Locator otherEmailTextField = driver.locator("//*[@id=\"Users_editView_fieldName_email2\"]");
	private Locator officePhoneTextField = driver.locator("//*[@id=\"Users_editView_fieldName_phone_work\"]");
	private Locator secondaryEmailTextField = driver.locator("//*[@id=\"Users_editView_fieldName_secondaryemail\"]");
	private Locator mobileTextField = driver.locator("//*[@id=\"Users_editView_fieldName_phone_mobile\"]");
	
	private Locator streetAddressTextField = driver.locator("//*[@id=\"Users_editView_fieldName_address_street\"]");
	private Locator cityTextField = driver.locator("//*[@id=\"Users_editView_fieldName_address_city\"]");
	private Locator stateTextField = driver.locator("//*[@id=\"Users_editView_fieldName_address_state\"]");
	private Locator countryDropdownField = driver.locator("#s2id_autogen28");
	private Locator countrySearchField = driver.locator("#s2id_autogen29_search");
	private Locator postalCodeTextField = driver.locator("//*[@id=\"Users_editView_fieldName_address_postalcode\"]");
	
	private Locator searchEmailAddress = driver.locator("//*[@name='first_name']");
	private Locator searchUserName = driver.locator("//*[@name='user_name']");
	private Locator searchUserButton = driver.locator("//*[@id=\"listview-table\"]/tbody/tr[1]/th[1]/div/button");

	private Locator userFullNameField = driver.locator("//*[@id=\"userPageHeader\"]/div/div[1]/span");
	private Locator searchResultText  = driver.locator("//*[@id=\"Users_listView_row_1\"]");
	

	public UserDetails getUserDetails(String dataFile) throws StreamReadException, DatabindException, IOException {
		log.info("Get User Details from file");
		String filePath = getClass().getClassLoader().getResource(dataFile).getPath();
		userDetails = (UserDetails) SkyHighUtil.readDetailsFromFile(filePath, UserDetails.class);
		return userDetails;
	}

	public void enterUserLoginDetails(UserLoginRole userLoginRole, String role) {
		log.info("Enter User Login Information");
		new ButtonElement(driver, addUserButton).click();
		driver.waitForLoadState();
		userName = userLoginRole.getUserName() + "-" + SkyHighUtil.generateRandomNumber(1000);
		emailAddress = userLoginRole.getEmail();
		
		if (StringUtils.isEmpty(emailAddress))  {
			emailAddress = "test-" + SkyHighUtil.generateRandomNumber(1000) + "@gmail.com";
		}
	
		log.info("User Name is {} and Email Address is {}", userName, emailAddress);
		new TextfieldElement(driver, userNameField).setValue(userName);
		new TextfieldElement(driver, firstNameField).setValue(userLoginRole.getFirstName());
		new TextfieldElement(driver, lastNameField).setValue(userLoginRole.getLastName());
		new TextfieldElement(driver, emailAddressField).setValue(emailAddress);
		new TextfieldElement(driver, passwordField).setSensitiveValue(userLoginRole.getPassword());
		new TextfieldElement(driver, confirmPasswordField).setSensitiveValue(userLoginRole.getConfirmPassword());

		new DropdownElement(driver, roleDropdownField).select2DropdownSelect(roleSearchField, role);
		new DropdownElement(driver, businessRegionDropdownField).select2DropdownSelect(businessRegionSearchField, userLoginRole.getBusinessRegion());
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public void submitAddUserPage() {
		new ButtonElement(driver, saveUserInformationButton).click();
		driver.waitForTimeout(5000); 
		driver.waitForLoadState();
		
	}

	public void enterCurrencyConfiguration(CurrencyConfiguration currencyConfiguration) {
		log.info("Enter Currency Configuration");
		
		new DropdownElement(driver, currencyDropdownField).select2DropdownSelect(currencySearchField, currencyConfiguration.getCurrency());
		new DropdownElement(driver, digitGroupingPattern).select2DropdownSelect(digitGroupingPatternSearch, currencyConfiguration.getDigitGroupingPattern());
		new DropdownElement(driver, decimalSeperator).select2DropdownSelect(decimalSeperatorSearch, currencyConfiguration.getDecimalSeperator());
		new DropdownElement(driver, symbolPlacement).select2DropdownSelect(symbolPlacementSearch, currencyConfiguration.getSymbolPlacement());
		new DropdownElement(driver, digitGroupingSeperator).select2DropdownSelect(digitGroupingSeperatorSearch, currencyConfiguration.getDigitGroupingSeperator());
		
		log.info("currencyConfiguration.getTruncateTrailingZeros()" + currencyConfiguration.getTruncateTrailingZeros());
		if (currencyConfiguration.getTruncateTrailingZeros() == true) {
			new ButtonElement(driver, truncateTrailingZero).click();
		}

	}

	public void enterMoreInformation(MoreInformationDetails moreInformationDetails) {
		log.info("Enter More Information");
		new TextfieldElement(driver, titleTextField).setValue(moreInformationDetails.getTitle());
		new TextfieldElement(driver, departmentTextField).setValue(moreInformationDetails.getFax());
		new TextfieldElement(driver, faxTextField).setValue(moreInformationDetails.getDepartment());
		new TextfieldElement(driver, otherEmailTextField).setValue(moreInformationDetails.getOtherEmail());
		new TextfieldElement(driver, officePhoneTextField).setValue(moreInformationDetails.getOfficePhone());
		new TextfieldElement(driver, secondaryEmailTextField).setValue(moreInformationDetails.getSecondaryEmail());
		new TextfieldElement(driver, mobileTextField).setValue(moreInformationDetails.getMobile());
	}

	public void enterAddressDetails(AddressDetails addressDetails) {
		log.info("Enter Address Information");
		new TextfieldElement(driver, streetAddressTextField).setValue(addressDetails.getStreetAddress());
		new TextfieldElement(driver, cityTextField).setValue(addressDetails.getCity());
		new TextfieldElement(driver, stateTextField).setValue(addressDetails.getState());
		new DropdownElement(driver, countryDropdownField).select2DropdownSelect(countrySearchField, addressDetails.getCountry());
		new TextfieldElement(driver, postalCodeTextField).setValue(Integer.toString(addressDetails.getPostcode()));
	}

	public void navigateCrmModule(String module, String subModule) {
		log.info("Navigate to User's Page");
		driver.locator("//span[contains(.,'" + module.toUpperCase() + "')]").click();
		Locator pageNameField = driver.locator("//a[contains(.,'" + subModule + "')]");
		driver.waitForTimeout(3000);
		new ButtonElement(driver, pageNameField).click();
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public void validateUserCreation() {
		log.info("Validate User Creation.");
		new TextfieldElement(driver, searchEmailAddress).setValue(this.emailAddress);
		new TextfieldElement(driver, searchUserName).setValue(this.userName);
		new ButtonElement(driver, searchUserButton).click();
		driver.waitForTimeout(80000);
		assertThat("No result is found", searchResultText.isVisible(), equalTo(true));
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public void selectUser(int dataSetID) {
		log.info("Select User from User's List");
		driver.waitForTimeout(5000); 
		Locator userResult = driver.locator("//*[@id=\"Users_listView_row_1\"][contains(.,'" + userName + "')]");
		new ButtonElement(driver, userResult).click();
		String userFirstName = userDetails.getTestData()[0].getData().get(dataSetID).getUserLoginRole().getFirstName();
		String userLastName = userDetails.getTestData()[0].getData().get(dataSetID).getUserLoginRole().getLastName();
		assertThat("User's Full Name Does not Match", userFullNameField.innerText(), equalTo(userFirstName + " " + userLastName));
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public String getUserName() {
		return this.userName;
	}
}
