package com.pages;

import org.junit.Assert;

import com.customer.PaymentDetails;
import com.elements.ButtonElement;
import com.elements.TextfieldElement;
import com.enums.PaymentOption;
import com.microsoft.playwright.FrameLocator;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;
import com.stepDefinition.TestContext;
import com.utils.SkyHighUtil;

public class SelfCarePaymentPage extends SkyHighPage  {
	
	private Locator paypalOption = driver.locator("//div[@aria-label='Paying with PayPal']");
	private Locator cardOption = driver.locator("//div[@aria-label='Paying with Card']");
	private FrameLocator iFrameCreditCardName = driver.frameLocator("//iframe[@id='braintree-hosted-field-cardholderName']");
	private Locator cardNameTextField = iFrameCreditCardName.locator("//*[@id='cardholder-name']");
	private FrameLocator iFrameCreditCardNumber = driver.frameLocator("//iframe[@id='braintree-hosted-field-number']");
	private Locator cardNumberTextField = iFrameCreditCardNumber.locator("//*[@id='credit-card-number']");
	private FrameLocator iFramecardExpiry = driver.frameLocator("//iframe[@id='braintree-hosted-field-expirationDate']");
	private Locator cardNumberExpiryTextField = iFramecardExpiry.locator("//*[@id='expiration']");
	private FrameLocator iframePaypalButton = driver.frameLocator("//div[@data-braintree-id='paypal-button']//iframe");
	private Locator paypalCheckoutButton = iframePaypalButton.locator("//img[@aria-label='paypal']");
	//private FrameLocator iFrameCVV = driver.frameLocator("//iframe[@id='braintree-hosted-field-cvv']");
	//private Locator CVVTextField = iFrameCVV.locator("//*[@id='cvv']");
	private Locator paypalEmailTextField = driver.locator("//*[@name='login_email']");
	private Locator paypalEmailNextButton = driver.locator("//*[@id='btnNext']");
	private Locator paypalPasswordTextField = driver.locator("//*[@id='password']");
	private Locator paypalLoginButton = driver.locator("//*[@id='btnLogin']");
	private Locator buttonAgreeAndContinue = driver.locator("//*[@id='consentButton']");
	private Locator paymentConsentBox = driver.locator("//*[@name='identityValidationCheck']/following::span[1]");
	private Locator nextButton =  driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Next"));
	
	public SelfCarePaymentPage(Page driver) {
		super(driver);
	}

	public void selectPaymentMethod(String paymentMethod) {
		log.info("Select Payment Method: {} ", paymentMethod);
		driver.waitForTimeout(2000);
		if (PaymentOption.PAYPAL.getDescription().equalsIgnoreCase(paymentMethod)) {
			driver.waitForCondition(() -> paypalOption.isEnabled());
			new ButtonElement(driver, paypalOption).click();
		} else {
			driver.waitForCondition(() -> cardOption.isEnabled());
			new ButtonElement(driver, cardOption).click();
		}
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}

	public Page openPaypalPage() {
		log.info("Open Paypal Page");
		Page newDriver = TestContext.getBrowserContext().waitForPage(() -> {
			paypalCheckoutButton.click(); // Opens a new tab
			});
		log.info("Page Title: {}",  newDriver.title());
		return newDriver;
	}

	public void enterPaymentDetails(String paymentMethod, PaymentDetails paymentDetails, int dataSetID) {
		switch (PaymentOption.fromDescription(paymentMethod)) {

		case PAYPAL:
			log.info("Enter Customer Paypal Details");
			driver.waitForCondition(() -> paypalEmailTextField.isVisible());
			new TextfieldElement(driver, paypalEmailTextField).setValue(paymentDetails.getPaypalEmail());
			paypalEmailNextButton.click();
			new TextfieldElement(driver, paypalPasswordTextField).setValue(paymentDetails.getPaypalPassword());
			paypalLoginButton.click();
			buttonAgreeAndContinue.click();
			SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
			break;

		case CREDIT_CARD:
			log.info("Enter Customer Credit Card Details");
			new TextfieldElement(driver, cardNameTextField).setValue(paymentDetails.getNameOnCard());
			new TextfieldElement(driver, cardNumberTextField).setValue(paymentDetails.getCardNumber());
			new TextfieldElement(driver, cardNumberExpiryTextField).setValue(paymentDetails.getCardExpiry());
			//new TextfieldElement(driver, CVVTextField).setValue(paymentDetails.getCardCVV());
			SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
			break;

		default:
			Assert.fail("Invalid Payment Type");
			break;

		}
	}
	
	public void submitPaymentPage() {
		log.info("Submit Payment Page");
		new ButtonElement(driver, nextButton).click();
		
	}

	public void checkConsentCheckbox() {
		log.info("Check Consent Checkbox");
		new ButtonElement(driver, paymentConsentBox).click();
		
	}
}