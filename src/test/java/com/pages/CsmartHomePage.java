package com.pages;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;

public class CsmartHomePage extends SkyHighPage {
		
	private Locator dashboardText = driver.locator("//*[@id=\"page\"]/div[4]/div/ul/li/a");
    
	public CsmartHomePage(Page driver) {
		super(driver);

	}

	public void validateHomePage() {
		log.info("Validate Home Page in Csmart");
		driver.waitForTimeout(10000);
		assertThat("Csmart Dashboard is not displayed", dashboardText.isVisible(), equalTo(true));
	}

}
