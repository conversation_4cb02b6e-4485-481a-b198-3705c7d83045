package com.pages;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import java.io.IOException;

import org.assertj.core.api.SoftAssertions;

import com.customer.AddressDetails;
import com.customer.CustomerDetails;
import com.customer.Data;
import com.customer.PaymentDetails;
import com.elements.ButtonElement;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.utils.SkyHighUtil;

public class SelfCareAccountPage extends SkyHighPage  {
	
	private Locator firstNameText = driver.locator("//p[contains(text(), 'First name')]/following::p[1]");
	private Locator lastNameText = driver.locator("//p[contains(text(), 'Last name')]/following::p[1]");
	private Locator middleNameText = driver.locator("//p[contains(text(), 'Middle name')]/following::p[1]");
	private Locator addressText = driver.locator("//p[contains(text(), 'Address')]/following::p[1]");
	private Locator stateText = driver.locator("//p[contains(text(), 'State')]/following::p[1]");
	private Locator suburbText = driver.locator("//p[contains(text(), 'Suburb')]/following::p[1]");
	private Locator postCode = driver.locator("//p[contains(text(), 'Postcode')]/following::p[1]");
	
	public SelfCareAccountPage(Page driver) {
		super(driver);
	}
	
	
	public void selectAccountDetailsMenu(String accountDetailsMenu) {
		log.info("Select Account Details Menu");
		Locator accountElement = driver.locator("//span[normalize-space()='" + accountDetailsMenu + "')]").first();
		new ButtonElement(driver, accountElement).click();
	}


	public CustomerDetails getCustomerDetails(String fileName) throws StreamReadException, DatabindException, IOException {
		log.info("Get Customer Details from File");
		String filePath = getClass().getClassLoader().getResource(fileName).getPath();
		CustomerDetails customerDetails = (CustomerDetails) SkyHighUtil.readDetailsFromFile(filePath, CustomerDetails.class);
		return customerDetails;
	}


	public void verifyPersonalInformation(Data data) {
		log.info("Verify Personal Information");
		SoftAssertions softAssertions = new SoftAssertions();
		softAssertions.assertThat(firstNameText.innerText().trim()).isEqualTo(data.getPrimaryContactDetails().getFirstName());
		softAssertions.assertThat(lastNameText.innerText().trim()).isEqualTo(data.getPrimaryContactDetails().getLastName());
		softAssertions.assertThat(middleNameText.innerText().trim()).isEqualTo(data.getPrimaryContactDetails().getMiddleName());
		softAssertions.assertAll();
	}


	public void verifyAddressInformation(AddressDetails addressDetails) {
		log.info("Verify Address Information");
		SoftAssertions softAssertions = new SoftAssertions();
		softAssertions.assertThat(addressText.innerText().trim()).isEqualTo(addressDetails.getAddress());
		softAssertions.assertThat(stateText.innerText().trim()).isEqualTo(addressDetails.getState());
		softAssertions.assertThat(suburbText.innerText().trim()).isEqualTo(addressDetails.getSuburb());
		softAssertions.assertThat(postCode.innerText().trim()).isEqualTo(addressDetails.getPostcode());
		softAssertions.assertAll();
	}


	public void verifyPaymentInformation(PaymentDetails paymentDetails) {
		// TODO Auto-generated method stub
		
	}

}
