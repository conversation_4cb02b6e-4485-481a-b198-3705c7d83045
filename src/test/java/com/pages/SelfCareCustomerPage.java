package com.pages;

import com.customer.PrimaryContactDetails;
import com.customer.AuthorisedContactDetails;
import com.elements.ButtonElement;
import com.elements.DropdownElement;
import com.elements.TextfieldElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;
import com.stepDefinition.GlobalVariables;
import com.utils.SkyHighUtil;

public class SelfCareCustomerPage extends SkyHighPage  {
	
	public SelfCareCustomerPage(Page driver) {
		super(driver);
	}
	
	private Locator firstNameTextField = driver.locator("//*[@id='firstName']");
	private Locator middleNameTextField = driver.locator("//*[@id='middleName']");
	private Locator lastNameTextField = driver.locator("//*[@id='lastName']");
	private Locator dobField = driver.locator("//*[@class='_root_q0nc1_1 _hasSuffix_q0nc1_51 dark flatpickr-input']");
	private Locator emailTextField = driver.locator("//*[@id='email']");
	private Locator passwordTextField = driver.locator("//*[@id='createPassword']");
	private Locator confirmPasswordTextField = driver.locator("//*[@id='confirmPassword']");
	private Locator pinCodeTextField = driver.locator("//*[@id='telephoneSecurityPin']");
	private Locator confirmPinCodeTextField = driver.locator("//*[@id='confirmTelephoneSecurityPin']");
	private Locator authorisedCheckbox = driver.locator("//*[@id=\"root\"]/div[2]/main/div/div/div/div/div[3]/div[3]/div/div/form/div[4]/label/span[1]");
	private Locator authorisedFirstNameField = driver.locator("//*[@id='authorizedFirstName']");
	private Locator authorisedLastNameField = driver.locator("//*[@id='authorizedLastName']");
	private Locator authorisedEmailField = driver.locator("//*[@id='authorizedEmail']");
	private Locator primaryContactCheckbox = driver.locator("//div[@class='_background_1qdkx_132']//span[@class='_customCheckbox_1o4hk_32 _sm_1o4hk_48']");
	private Locator relationshipDropdownField =  driver.locator("//label[@for='relationshipAuthorized']/following-sibling::div//div[contains(@class, '_trigger_lr0wu_10')]");
	private Locator authorisedContactNumber = driver.locator("//label[span[text()='Authorised contact mobile number']]//following::input[contains(@class, 'dark')][1]");
	private Locator nextButton =  driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Next"));
	//private Locator addSecurityQuestionsButton =  driver.locator("//h6[normalize-space()='Add another question']");
	private Locator selectSecurityQuestionDropdown =  driver.locator("//div[@class='_fieldRow_1qdkx_42']//div[@class='_trigger_lr0wu_10'][normalize-space()='Select an option']").first();
	private Locator firstOptionSelected =  driver.locator("div._option_lr0wu_14").nth(0);
	private Locator answerTextField =  driver.locator("//*[@id='securityQuestion.0.answer']");
	

	public void enterPrimaryContactDetails(PrimaryContactDetails primaryContactDetails, String customerType) {
		log.info("Enter Primary Contact Details");
		new TextfieldElement(driver, firstNameTextField).setValue(primaryContactDetails.getFirstName());
		new TextfieldElement(driver, middleNameTextField).setValue(primaryContactDetails.getMiddleName());
		new TextfieldElement(driver, lastNameTextField).setValue(primaryContactDetails.getLastName());
		new TextfieldElement(driver, dobField.first()).setValue(primaryContactDetails.getDateOfBirth());
		new ButtonElement(driver, primaryContactCheckbox).click();
		new TextfieldElement(driver, emailTextField).setValue("aldi-" + SkyHighUtil.generateRandomNumber(1000) + "@gmail.com");
		new TextfieldElement(driver, passwordTextField).setSensitiveValue(primaryContactDetails.getPassword());
		new TextfieldElement(driver, confirmPasswordTextField).setSensitiveValue(primaryContactDetails.getConfirmPassword());
		GlobalVariables.setPassword(primaryContactDetails.getPassword());
		new TextfieldElement(driver, pinCodeTextField).setSensitiveValue(primaryContactDetails.getPinCode());
		new TextfieldElement(driver, confirmPinCodeTextField).setSensitiveValue(primaryContactDetails.getConfirmPinCode());
		driver.waitForTimeout(2000);

		new DropdownElement(driver, selectSecurityQuestionDropdown).click();
		new DropdownElement(driver, firstOptionSelected).click();
		new TextfieldElement(driver, answerTextField).setSensitiveValue(primaryContactDetails.getFirstName());

		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

	public void enterAuthorisedContactDetails(AuthorisedContactDetails authorisedContactDetails) {
		log.info("Enter Authorised Contact Details");
		driver.waitForTimeout(2000);
		
		if (!authorisedCheckbox.isChecked()) 
			new ButtonElement(driver, authorisedCheckbox).click();
		new TextfieldElement(driver, authorisedFirstNameField).setValue(authorisedContactDetails.getAuthorisedContactFirstName());
		new TextfieldElement(driver, authorisedLastNameField).setValue(authorisedContactDetails.getAuthorisedContactLastName());
		new TextfieldElement(driver, dobField.last()).setValue(authorisedContactDetails.getAuthorisedContactDob());
		driver.waitForTimeout(2000);
		new TextfieldElement(driver, authorisedContactNumber).setValue(authorisedContactDetails.getAuthorisedContactPhoneNumber());
		new DropdownElement(driver, relationshipDropdownField).click();
		new DropdownElement(driver, firstOptionSelected).click();
		new TextfieldElement(driver, authorisedEmailField).setValue("test-" + SkyHighUtil.generateRandomNumber(1000) + "@gmail.com");
		SkyHighUtil.takeScreenshot(driver, new Object() {}.getClass().getEnclosingMethod().getName());
	}

	public void submitCustomerPage() {
		new ButtonElement(driver, nextButton).click();
		
	}

}
