package com.pages;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import java.io.IOException;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import com.elements.ButtonElement;
import com.elements.TextfieldElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;
import com.stepDefinition.GlobalVariables;
import com.utils.SkyHighUtil;

public class SelfCareLoginPage extends SkyHighPage {
	
	private String userName;
	private String password;
	private Locator usernameField = driver.locator("//*[@id=\"accountNumber\"]");
	private Locator passwordField = driver.locator("//*[@id=\"password\"]");
	private Locator loginButton = driver.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Login"));
	private Locator logOutButton = driver.getByRole(AriaRole.LINK, new Page.GetByRoleOptions().setName("Logout"));

    public SelfCareLoginPage(Page driver) {
    	super(driver);
    }
    
	private String url;
	
	public void setSelfCareURL(String selfCareURL) {
		this.url = selfCareURL;	
	}
   

	public void setPassword(String password) {
		this.password = password;
	}
	
	public void setUserName(String userName) {
		this.userName = userName;
	}

	public void enterURL() {
		log.info("SelfCare URL: {}" + url);
		driver.navigate(url);
	}

	public void verifyUserLoggedIn() {
		log.info("Verify if user is already logged in");
		
			if (usernameField.isVisible()) {
				log.info("User is not yet logged-in in SelfCare. Logging in..");
				new TextfieldElement(driver, usernameField).setValue(userName);
				new TextfieldElement(driver, passwordField).setSensitiveValue(password);
				new ButtonElement(driver, loginButton).click();
				
			} else {
				log.info("User already logged-in to SelfCare.");
			}
			
			SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}


	public void validateLoginAccount() {
		log.info("Validate log-in credentials");

		if (StringUtils.isEmpty(this.userName)) {
			String serviceNumber = GlobalVariables.getServiceNumber();
			if (StringUtils.isNotEmpty(serviceNumber)) {
				assertThat("Service Number is invalid. Should start with 04 : " + serviceNumber,serviceNumber.startsWith("04"), equalTo(true));
				userName = serviceNumber;
			} else {
				Assert.fail("Provide service number.");
			}
		}
	}

	public void clickLogoutButton() {
		log.info("User logs out from SelfCare Mobile Website");
		new ButtonElement(driver, logOutButton).click();
	}

	public void verifyPageStatus() throws IOException {
		log.info("Verify if Page is Accessible");
		
		int responseCode = SkyHighUtil.getResponseCode(url);
		if (!(responseCode > 400)) {
			log.info("SelfCare Website is accessible. Proceed to Login");
		 } else {
			Assert.fail("SelfCare Website is Not Accessible");
		}
		
	}
}
