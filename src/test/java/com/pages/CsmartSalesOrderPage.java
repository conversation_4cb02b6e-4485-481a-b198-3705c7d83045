package com.pages;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import org.junit.Assert;

import com.elements.ButtonElement;
import com.elements.TextfieldElement;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.utils.SkyHighUtil;

public class CsmartSalesOrderPage extends SkyHighPage {
		
	Locator salesOrderSearchField = driver.locator("//*[@name='salesorder_no']");
	Locator searchButton = driver.locator("//button[@class='btn btn-success btn-sm']");
	private Locator orderSearchResult = driver.locator("//*[@id=\"SalesOrder_listView_row_1\"]/*[@data-name='sostatus']");
	
	public CsmartSalesOrderPage(Page driver) {
		super(driver);

	}

	public void validateOrderStatus(String status) {
		log.info("Validate Order Status");
		
		for (int minute = 1; minute <= 5; minute++) {
			new TextfieldElement(driver, salesOrderSearchField).setValue(SelfCareSubmissionPage.getOrderNumber());
			new ButtonElement(driver, searchButton).click();
			driver.waitForTimeout(2000);
			String orderStatus = orderSearchResult.innerText().trim();
			if (status.equalsIgnoreCase(orderStatus)) {
				break;
			} else if ("Failed".equalsIgnoreCase(orderStatus)) {
				Assert.fail("Order Number Failed");
				break;
			} else {
				log.info("No Order Status is displayed where expected.");
			}
			log.info("Current Order status: {} Refresh: {} min", orderStatus, minute);
			driver.waitForTimeout(60000);
			driver.reload();
			waitForPageToLoad();
		}
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
		assertThat("On Order Status", orderSearchResult.innerText(), equalTo(status));
		
	}
}
