package com.customer;

import java.io.Serializable;

public class ImportDetails implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	String existingProvider;
	String existingAccountNumber;
	String existingMobileNumber;

	public String getExistingProvider() {
		return existingProvider;
	}

	public void setExistingProvider(String existingProvider) {
		this.existingProvider = existingProvider;
	}

	public String getExistingAccountNumber() {
		return existingAccountNumber;
	}

	public void setExistingAccountNumber(String existingAccountNumber) {
		this.existingAccountNumber = existingAccountNumber;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getExistingMobileNumber() {
		return existingMobileNumber;
	}

	public void setExistingMobileNumber(String existingMobileNumber) {
		this.existingMobileNumber = existingMobileNumber;
	}

}
