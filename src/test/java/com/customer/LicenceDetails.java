package com.customer;

import java.io.Serializable;

public class LicenceDetails implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	String driversLicenceState;
	String driversLicenceNumber;
	String driversLicenceCardNumber;

	public String getDriversLicenceState() {
		return driversLicenceState;
	}

	public void setDriversLicenceState(String driversLicenceState) {
		this.driversLicenceState = driversLicenceState;
	}

	public String getDriversLicenceNumber() {
		return driversLicenceNumber;
	}

	public void setDriversLicenceNumber(String driversLicenceNumber) {
		this.driversLicenceNumber = driversLicenceNumber;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getDriversLicenceCardNumber() {
		return driversLicenceCardNumber;
	}

	public void setDriversLicenceCardNumber(String driversLicenceCardNumber) {
		this.driversLicenceCardNumber = driversLicenceCardNumber;
	}
	
}
