package com.customer;

import java.io.Serializable;

public class Data implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	Integer id;
	String userName;
	String password;
	String fpProviderNickname;
	String fpConsumerNickname;
	ImportDetails importDetails;
	PrimaryContactDetails primaryContactDetails;
	Boolean enterAuthorisedContactDetails;
	AuthorisedContactDetails authorisedContactDetails;
	AddressDetails addressDetails;
	PassportDetails passportDetails;
	MedicareDetails medicareDetails;
	LicenceDetails licenceDetails;
	String rechargePlanDetails;
	String rechargeVoucherCodeDetails;
	String rechargePackDetails;
	String idType;
	PaymentDetails paymentDetails;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getFpProviderNickname() {
		return fpProviderNickname;
	}

	public void setFpProviderNickname(String fpProviderNickname) {
		this.fpProviderNickname = fpProviderNickname;
	}

	public String getFpConsumerNickname() {
		return fpConsumerNickname;
	}

	public void setFpConsumerNickname(String fpConsumerNickname) {
		this.fpConsumerNickname = fpConsumerNickname;
	}

	public ImportDetails getImportDetails() {
		return importDetails;
	}

	public void setImportDetails(ImportDetails importDetails) {
		this.importDetails = importDetails;
	}

	public PrimaryContactDetails getPrimaryContactDetails() {
		return primaryContactDetails;
	}

	public void setPrimaryContactDetails(PrimaryContactDetails primaryContactDetails) {
		this.primaryContactDetails = primaryContactDetails;
	}

	public Boolean getEnterAuthorisedContactDetails() {
		return enterAuthorisedContactDetails;
	}

	public void setEnterAuthorisedContactDetails(Boolean enterAuthorisedContactDetails) {
		this.enterAuthorisedContactDetails = enterAuthorisedContactDetails;
	}

	public AuthorisedContactDetails getAuthorisedContactDetails() {
		return authorisedContactDetails;
	}

	public void setAuthorisedContactDetails(AuthorisedContactDetails authorisedContactDetails) {
		this.authorisedContactDetails = authorisedContactDetails;
	}

	public AddressDetails getAddressDetails() {
		return addressDetails;
	}

	public void setAddressDetails(AddressDetails addressDetails) {
		this.addressDetails = addressDetails;
	}

	public PassportDetails getPassportDetails() {
		return passportDetails;
	}

	public void setPassportDetails(PassportDetails passportDetails) {
		this.passportDetails = passportDetails;
	}

	public MedicareDetails getMedicareDetails() {
		return medicareDetails;
	}

	public void setMedicareDetails(MedicareDetails medicareDetails) {
		this.medicareDetails = medicareDetails;
	}

	public LicenceDetails getLicenceDetails() {
		return licenceDetails;
	}

	public void setLicenceDetails(LicenceDetails licenceDetails) {
		this.licenceDetails = licenceDetails;
	}

	public String getRechargePlanDetails() {
		return rechargePlanDetails;
	}

	public void setRechargePlanDetails(String rechargePlanDetails) {
		this.rechargePlanDetails = rechargePlanDetails;
	}

	public String getRechargeVoucherCodeDetails() {
		return rechargeVoucherCodeDetails;
	}

	public void setRechargeVoucherCodeDetails(String rechargeVoucherCodeDetails) {
		this.rechargeVoucherCodeDetails = rechargeVoucherCodeDetails;
	}

	public String getRechargePackDetails() {
		return rechargePackDetails;
	}

	public void setRechargePackDetails(String rechargePackDetails) {
		this.rechargePackDetails = rechargePackDetails;
	}

	public String getIdType() {
		return idType;
	}

	public void setIdType(String idType) {
		this.idType = idType;
	}

	public PaymentDetails getPaymentDetails() {
		return paymentDetails;
	}

	public void setPaymentDetails(PaymentDetails paymentDetails) {
		this.paymentDetails = paymentDetails;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
