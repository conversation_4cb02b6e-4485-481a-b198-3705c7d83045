package com.customer;

import java.io.Serializable;

public class CustomerDetails implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	String feature;
	TestData [] testData;

	public String getFeature() {
		return feature;
	}

	public void setFeature(String feature) {
		this.feature = feature;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public TestData[] getTestData() {
		return testData;
	}

	public void setTestData(TestData[] testData) {
		this.testData = testData;
	}

}
