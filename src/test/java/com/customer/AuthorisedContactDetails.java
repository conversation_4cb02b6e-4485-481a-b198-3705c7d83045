package com.customer;

import java.io.Serializable;

public class AuthorisedContactDetails implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	String authorisedContactRelationship;
	String authorisedContactTitle;
	String authorisedContactFirstName;
	String authorisedContactLastName;
	String authorisedContactPhoneNumber;
	String authorisedContactMobilePhoneNumber;
	String authorisedContactEmail;
	String authorisedContactDob;

	
	public static long getSerialversionuid() {
		return serialVersionUID;
	}


	public String getAuthorisedContactRelationship() {
		return authorisedContactRelationship;
	}


	public void setAuthorisedContactRelationship(String authorisedContactRelationship) {
		this.authorisedContactRelationship = authorisedContactRelationship;
	}


	public String getAuthorisedContactTitle() {
		return authorisedContactTitle;
	}


	public void setAuthorisedContactTitle(String authorisedContactTitle) {
		this.authorisedContactTitle = authorisedContactTitle;
	}


	public String getAuthorisedContactFirstName() {
		return authorisedContactFirstName;
	}


	public void setAuthorisedContactFirstName(String authorisedContactFirstName) {
		this.authorisedContactFirstName = authorisedContactFirstName;
	}


	public String getAuthorisedContactLastName() {
		return authorisedContactLastName;
	}


	public void setAuthorisedContactLastName(String authorisedContactLastName) {
		this.authorisedContactLastName = authorisedContactLastName;
	}


	public String getAuthorisedContactPhoneNumber() {
		return authorisedContactPhoneNumber;
	}


	public void setAuthorisedContactPhoneNumber(String authorisedContactPhoneNumber) {
		this.authorisedContactPhoneNumber = authorisedContactPhoneNumber;
	}


	public String getAuthorisedContactMobilePhoneNumber() {
		return authorisedContactMobilePhoneNumber;
	}


	public void setAuthorisedContactMobilePhoneNumber(String authorisedContactMobilePhoneNumber) {
		this.authorisedContactMobilePhoneNumber = authorisedContactMobilePhoneNumber;
	}


	public String getAuthorisedContactEmail() {
		return authorisedContactEmail;
	}


	public void setAuthorisedContactEmail(String authorisedContactEmail) {
		this.authorisedContactEmail = authorisedContactEmail;
	}


	public String getAuthorisedContactDob() {
		return authorisedContactDob;
	}


	public void setAuthorisedContactDob(String authorisedContactDob) {
		this.authorisedContactDob = authorisedContactDob;
	}
}
