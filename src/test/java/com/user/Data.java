package com.user;

import java.io.Serializable;

public class Data implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	UserLoginRole userLoginRole;
	CurrencyConfiguration currencyConfiguration;
	MoreInformationDetails moreInformationDetails;
	AddressDetails addressDetails;

	public UserLoginRole getUserLoginRole() {
		return userLoginRole;
	}

	public void setUserLoginRole(UserLoginRole userLoginRole) {
		this.userLoginRole = userLoginRole;
	}

	public CurrencyConfiguration getCurrencyConfiguration() {
		return currencyConfiguration;
	}

	public void setCurrencyConfiguration(CurrencyConfiguration currencyConfiguration) {
		this.currencyConfiguration = currencyConfiguration;
	}

	public MoreInformationDetails getMoreInformationDetails() {
		return moreInformationDetails;
	}

	public void setMoreInformationDetails(MoreInformationDetails moreInformationDetails) {
		this.moreInformationDetails = moreInformationDetails;
	}

	public AddressDetails getAddressDetails() {
		return addressDetails;
	}

	public void setAddressDetails(AddressDetails addressDetails) {
		this.addressDetails = addressDetails;
	}


}
