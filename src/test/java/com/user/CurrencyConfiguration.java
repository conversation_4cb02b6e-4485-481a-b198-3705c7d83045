package com.user;

import java.io.Serializable;

public class CurrencyConfiguration implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	String currency;
	String decimalSeperator;
	String symbolPlacement;
	String digitGroupingPattern;
	String digitGroupingSeperator;
	Boolean truncateTrailingZeros;
	
	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getDecimalSeperator() {
		return decimalSeperator;
	}

	public void setDecimalSeperator(String decimalSeperator) {
		this.decimalSeperator = decimalSeperator;
	}

	public String getSymbolPlacement() {
		return symbolPlacement;
	}

	public void setSymbolPlacement(String symbolPlacement) {
		this.symbolPlacement = symbolPlacement;
	}

	public String getDigitGroupingPattern() {
		return digitGroupingPattern;
	}

	public void setDigitGroupingPattern(String digitGroupingPattern) {
		this.digitGroupingPattern = digitGroupingPattern;
	}

	public String getDigitGroupingSeperator() {
		return digitGroupingSeperator;
	}

	public void setDigitGroupingSeperator(String digitGroupingSeperator) {
		this.digitGroupingSeperator = digitGroupingSeperator;
	}

	public Boolean getTruncateTrailingZeros() {
		return truncateTrailingZeros;
	}

	public void setTruncateTrailingZeros(Boolean truncateTrailingZeros) {
		this.truncateTrailingZeros = truncateTrailingZeros;
	}

}
