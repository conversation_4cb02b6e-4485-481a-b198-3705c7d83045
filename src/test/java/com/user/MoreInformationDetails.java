package com.user;

import java.io.Serializable;

public class MoreInformationDetails implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	String title;
	String department;
	String officePhone;
	String mobile;
	String homePhone;
	String fax;
	String otherEmail;
	Boolean internalMailComposer;
	String crmPhoneExtension;
	Boolean leftPanelHide;
	String language;
	String defaultRecordView;
	String secondaryEmail;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getOfficePhone() {
		return officePhone;
	}

	public void setOfficePhone(String officePhone) {
		this.officePhone = officePhone;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getHomePhone() {
		return homePhone;
	}

	public void setHomePhone(String homePhone) {
		this.homePhone = homePhone;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getOtherEmail() {
		return otherEmail;
	}

	public void setOtherEmail(String otherEmail) {
		this.otherEmail = otherEmail;
	}

	public Boolean getInternalMailComposer() {
		return internalMailComposer;
	}

	public void setInternalMailComposer(Boolean internalMailComposer) {
		this.internalMailComposer = internalMailComposer;
	}

	public String getCrmPhoneExtension() {
		return crmPhoneExtension;
	}

	public void setCrmPhoneExtension(String crmPhoneExtension) {
		this.crmPhoneExtension = crmPhoneExtension;
	}

	public Boolean getLeftPanelHide() {
		return leftPanelHide;
	}

	public void setLeftPanelHide(Boolean leftPanelHide) {
		this.leftPanelHide = leftPanelHide;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String getDefaultRecordView() {
		return defaultRecordView;
	}

	public void setDefaultRecordView(String defaultRecordView) {
		this.defaultRecordView = defaultRecordView;
	}

	public String getSecondaryEmail() {
		return secondaryEmail;
	}

	public void setSecondaryEmail(String secondaryEmail) {
		this.secondaryEmail = secondaryEmail;
	}
}
