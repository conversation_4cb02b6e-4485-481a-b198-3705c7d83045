package com.user;

import java.io.Serializable;

public class UserLogin<PERSON>ole implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	String userName;
	String firstName;
	String lastName;
	String password;
	String confirmPassword;
	String email;
	String businessRegion;
	String currencyLimit;
	String ipAddress;
	String voiceLimit;
	String promoAdjustmentLimit;
	String dataAdjustmentLimit;
	String smsAdjustmentLimit;
	String tenancyName;
	String verificationToken;

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getConfirmPassword() {
		return confirmPassword;
	}

	public void setConfirmPassword(String confirmPassword) {
		this.confirmPassword = confirmPassword;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getBusinessRegion() {
		return businessRegion;
	}

	public void setBusinessRegion(String businessRegion) {
		this.businessRegion = businessRegion;
	}

	public String getCurrencyLimit() {
		return currencyLimit;
	}

	public void setCurrencyLimit(String currencyLimit) {
		this.currencyLimit = currencyLimit;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getVoiceLimit() {
		return voiceLimit;
	}

	public void setVoiceLimit(String voiceLimit) {
		this.voiceLimit = voiceLimit;
	}

	public String getPromoAdjustmentLimit() {
		return promoAdjustmentLimit;
	}

	public void setPromoAdjustmentLimit(String promoAdjustmentLimit) {
		this.promoAdjustmentLimit = promoAdjustmentLimit;
	}

	public String getDataAdjustmentLimit() {
		return dataAdjustmentLimit;
	}

	public void setDataAdjustmentLimit(String dataAdjustmentLimit) {
		this.dataAdjustmentLimit = dataAdjustmentLimit;
	}

	public String getSmsAdjustmentLimit() {
		return smsAdjustmentLimit;
	}

	public void setSmsAdjustmentLimit(String smsAdjustmentLimit) {
		this.smsAdjustmentLimit = smsAdjustmentLimit;
	}

	public String getTenancyName() {
		return tenancyName;
	}

	public void setTenancyName(String tenancyName) {
		this.tenancyName = tenancyName;
	}

	public String getVerificationToken() {
		return verificationToken;
	}

	public void setVerificationToken(String verificationToken) {
		this.verificationToken = verificationToken;
	}

}
