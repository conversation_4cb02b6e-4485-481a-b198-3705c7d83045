package com.stepDefinition;


import java.util.List;

public class GlobalVariables {
	
	private static ThreadLocal<String> accountNumber = new ThreadLocal<String>();
	private static ThreadLocal<String> password = new ThreadLocal<String>();
	private static ThreadLocal<String> serviceNumber = new ThreadLocal<String>();
	private static ThreadLocal<List<String>> consumerList = new ThreadLocal<List<String>>();

	public static void setAccountNumber(String accountNumber) {
		GlobalVariables.accountNumber.set(accountNumber);
	}
	
	public static String getAccountNumber() {
		
		if(GlobalVariables.accountNumber.get() == null) {
			GlobalVariables.accountNumber.set(System.getProperty("accountnumber").trim());
		}
		return GlobalVariables.accountNumber.get();
		
	}

	public static String getPassword() {

		if (GlobalVariables.password.get() == null) {
			GlobalVariables.password.set(System.getProperty("password"));
		}
		return GlobalVariables.password.get();
	}

	public static void setPassword(String password) {
		GlobalVariables.password.set(password);
	}
	
	public static void setServiceNumber(String serviceNumber) {
		GlobalVariables.serviceNumber.set(serviceNumber);
	}
	
	public static String getServiceNumber() {
		
		if(GlobalVariables.serviceNumber.get() == null) {
			GlobalVariables.serviceNumber.set(System.getProperty("serviceNumber").trim());
		}		
		return GlobalVariables.serviceNumber.get();
	}

	public static List<String> getConsumerList() {
		return GlobalVariables.consumerList.get();
	}

	public static void setConsumerList(List<String> consumerList) {
		GlobalVariables.consumerList.set(consumerList);
	}	

}


