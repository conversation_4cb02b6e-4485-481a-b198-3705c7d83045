package com.stepDefinition;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;

import io.cucumber.java.Scenario;

public class TestContext {
	private static ThreadLocal<Scenario> threadLocalScenario = new ThreadLocal<>();
	private static ThreadLocal<Playwright> threadLocalPlaywright = new ThreadLocal<>();
	private static ThreadLocal<Browser> threadLocalBrowser = new ThreadLocal<>();
	private static ThreadLocal<BrowserContext> threadLocalContext = new ThreadLocal<>();
	private static ThreadLocal<Page> threadLocalPage = new ThreadLocal<>();

	public static Scenario getScenario() {
		return threadLocalScenario.get();
	}

	public static void setScenario(Scenario scenario) {
		threadLocalScenario.set(scenario);
	}

	public static Playwright getPlaywright() {
		return threadLocalPlaywright.get();
	}

	public static void setPlaywright(Playwright playwright) {
		threadLocalPlaywright.set(playwright);
	}

	public static Browser getBrowser() {
		return threadLocalBrowser.get();
	}

	public static void setBrowser(Browser browser) {
		threadLocalBrowser.set(browser);
	}

	public static BrowserContext getBrowserContext() {
		return threadLocalContext.get();
	}

	public static void setBrowserContext(BrowserContext context) {
		threadLocalContext.set(context);
	}

	public static Page getPage() {
		return threadLocalPage.get();
	}

	public static void setPage(Page page) {
		threadLocalPage.set(page);
	}

	public static void remove() {
		threadLocalScenario.remove();
		threadLocalPlaywright.remove();
		threadLocalBrowser.remove();
		threadLocalContext.remove();
		threadLocalPage.remove();
	}

}
