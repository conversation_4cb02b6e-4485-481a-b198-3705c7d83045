package com.stepDefinition;

import java.io.IOException;

import org.slf4j.Logger;

import com.customer.CustomerDetails;
import com.enums.ActivationType;
import com.enums.CustomerType;
import com.enums.PaymentType;
import com.enums.PlanSelection;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.file.WriteToFile;
import com.init.LogInitializer;
import com.microsoft.playwright.Page;
import com.opencsv.exceptions.CsvException;
import com.pages.SelfCareSubmissionPage;
import com.pages.CsmartInventoryPage;
import com.pages.CsmartLoginPage;
import com.pages.CsmartRechargeVoucherPage;
import com.pages.SelfCareActivationPage;
import com.pages.SelfCareAddressPage;
import com.pages.SelfCareConfirmationPage;
import com.pages.SelfCareCustomerPage;
import com.pages.SelfCarePaymentPage;
import com.pages.SelfCarePlanPage;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;

public class SelfCareServiceSteps {

	private Page driver;
	private Logger log;
	private SelfCareActivationPage activatePage;
	private SelfCareCustomerPage customerPage;
	private SelfCareAddressPage addressPage;
    private CsmartInventoryPage inventory;
    private CsmartLoginPage csmartLogin;
	private SelfCarePlanPage planPage;
	private SelfCarePaymentPage paymentPage;
	private SelfCareConfirmationPage confirmation;
	private SelfCareSubmissionPage submissionPage;
	private WriteToFile write;
	private CsmartRechargeVoucherPage rechargePage;
	
	public SelfCareServiceSteps(DriverSteps driverSteps) {
		driver = driverSteps.getDriver();
		log = LogInitializer.getLogger(CsmartCustomerSteps.class);
		activatePage = new SelfCareActivationPage(driver);
		customerPage = new SelfCareCustomerPage(driver);
		addressPage = new SelfCareAddressPage(driver);
		planPage = new SelfCarePlanPage(driver);
		paymentPage = new SelfCarePaymentPage(driver);
		inventory = new CsmartInventoryPage(driver);
		csmartLogin = new CsmartLoginPage(driver);
		confirmation = new SelfCareConfirmationPage(driver);
		submissionPage = new SelfCareSubmissionPage(driver);
		write = new WriteToFile();
		rechargePage = new CsmartRechargeVoucherPage(driver);
	}
	
	private int dataSetID;
	private String customerType;
	private String activationType;
	private String simType;
	private CustomerDetails customerDetails;
	private String[] activationCodeDetails;
	private String[] voucherCodeDetails;
	String tenant = System.getProperty("tenant");
	private String file = DriverSteps.getEnvProperty("invoice.file");
	
	@And("{string} {string} activates a new {string}")
	public void user_activates_a_new_sim(String customerType, String userDataId, String simType) {
		this.customerType = customerType;
		this.simType = simType;
		this.dataSetID = Integer.parseInt(userDataId) - 1;
		activatePage.initiateActivation();
		activatePage.selectSimType(this.simType);
		activatePage.submitSimTypePage();
	}
	
	@And("Customer enters activation code from starter pack")
	public void enters_activation_code_from_starter_pack() throws StreamReadException, DatabindException, IOException, CsvException {
	
		String activationCode = activationCodeDetails[0];
		log.info("Activation Code is: {}", activationCode);
		activatePage.enterActivationCode(activationCode);
		activatePage.verifyActivationCodePage();
		activatePage.submitActivationCodePage();
	}
	
	@And("Customer selects {string} number")
	public void user_selects_number(String activationType) throws StreamReadException, DatabindException, IOException, CsvException {
		this.activationType = activationType;
		activatePage.selectNumberType(activationType);
		String testDataFile = DriverSteps.getEnvProperty("testData.file");
		
		if (ActivationType.PORT.getDescription().equalsIgnoreCase(activationType)) {
			int testDataIndex = activatePage.getTestDataIndex(activationType);
			activatePage.enterPortingDetails(customerDetails.getTestData()[testDataIndex].getData().get(dataSetID).getImportDetails(), testDataFile);
		} else {
			activatePage.selectServiceNumber();
		}
		
		activatePage.submitNumberPage();
	}
	
	@And("Customer performs number transfer verification for Port Orders")
	public void customer_performs_number_transfer() throws StreamReadException, DatabindException, IOException, CsvException {
		
		if (ActivationType.PORT.getDescription().equalsIgnoreCase(this.activationType)) {
			activatePage.performVerification();
			activatePage.submitNumberPage();
		} 
	}
	
	@And("Enters customer details in customer details page")
	public void enters_customer_details_in_customer_details_page() throws StreamReadException, DatabindException, IOException {
		
		customerDetails = activatePage.getCustomerDetails(DriverSteps.getEnvProperty("customer.details"));
		
		if (!CustomerType.EXISTING_CUSTOMER.getDescription().equalsIgnoreCase(customerType)) {

			customerPage.enterPrimaryContactDetails(customerDetails.getTestData()[0].getData().get(dataSetID).getPrimaryContactDetails(),customerType);
			if (customerDetails.getTestData()[dataSetID].getData().get(0).getEnterAuthorisedContactDetails().equals(true)) {
				customerPage.enterAuthorisedContactDetails(customerDetails.getTestData()[0].getData().get(dataSetID).getAuthorisedContactDetails());
			}
		}

		customerPage.submitCustomerPage();
	}

	@Then("Enters address details in address details page")
	public void enters_address_details_in_customer_address_page() throws StreamReadException, DatabindException, IOException {
		
		if (!CustomerType.EXISTING_CUSTOMER.getDescription().equalsIgnoreCase(customerType)) {
			addressPage.enterAddressDetails(customerDetails.getTestData()[0].getData().get(dataSetID).getAddressDetails());
		} else {
			customerDetails = activatePage.getCustomerDetails("testData/customerDetails.json");
			addressPage.enterAddressDetails(customerDetails.getTestData()[1].getData().get(dataSetID).getAddressDetails());
		}
		
		addressPage.submitAddressPage();
	}
	
	@Then("Customer selects {string} with {string}")
	public void customer_selects_plan(String planOption, String planName)  {
		
		planPage.selectPlanOption(planOption);
		planPage.selectPlanName(planName);
		String amount = planPage.getAmountPayable();
		write.writeToFile(amount, file);
	}
	
	@Then("Customer selects {string} {string} with {string}")
	public void customer_selects_plan(String planOption, String planType, String planName)  {
		
		planPage.selectPlanOption(planOption);
		planPage.selectPlanType(planType);
		planPage.selectPlanName(planName);
		String amountPayable = planPage.getAmountPayable();
		write.writeToFile(amountPayable, file);

	}

	@Then("Set Plan Auto Recharge status to {string}")
	public void set_auto_recharge_status(String autoRechargeStatus)  {
		
		planPage.setAutoRechargeStatus(autoRechargeStatus);
		planPage.submitPlanPage();
	}
	
	@Then("Customer selects {string}")
	public void customer_selects_plan(String planOption)  {
		
		planPage.selectPlanOption(planOption);
		if (PlanSelection.VOUCHER_CODE.getDescription().equalsIgnoreCase(planOption)) {
			planPage.enterVoucherCode(voucherCodeDetails[1]);
			planPage.validateVoucherCode();
			String amountPayable = planPage.getAmountPayable();
			write.writeToFile(amountPayable, file);
		}
	}

	@Then("Customer enters {string} payment details")
	public void customer_enters_payment_details(String paymentMethod)  {
		
		int testDataIndex = activatePage.getTestDataIndex(customerType);
		paymentPage.selectPaymentMethod(paymentMethod);
		
		if (PaymentType.PAYPAL.getDescription().equalsIgnoreCase(paymentMethod)) {
			getDriverPaypal();
			paymentPage.enterPaymentDetails(paymentMethod, customerDetails.getTestData()[testDataIndex].getData().get(dataSetID).getPaymentDetails(), dataSetID);
			setDefaultDriver();
		} else {
			paymentPage.enterPaymentDetails(paymentMethod, customerDetails.getTestData()[testDataIndex].getData().get(dataSetID).getPaymentDetails(), dataSetID);
		}
		
		paymentPage.checkConsentCheckbox();
		paymentPage.submitPaymentPage();
	}
	
	@And("User selects {string} for {string} with {string} status")
	public void user_selects_data(String data, String productType, String initialStatus) {
		
		inventory.setUserName(csmartLogin.getUserName());
		inventory.searchData(productType, initialStatus);
		activationCodeDetails = inventory.getActivationCodeDetails();
	}
	
	@And("User selects {string} with {string} status")
	public void user_selects_data(String data, String initialStatus) {
		
		inventory.setUserName(csmartLogin.getUserName());
		inventory.searchData("Voucher Code", initialStatus);
		voucherCodeDetails = rechargePage.getVoucherCodeDetails();
	}
	
	@And("User submits activation order upon reviewing customer details")
	public void user_submits_activation_order() {
		
		confirmation.reviewOrder();
		confirmation.getServiceNumber();
		activatePage.submitActivationPage();
	}
	
	@And("Activation order number and account number are displayed upon submission")
	public void activation_order_number_and_account_number() {
		
		submissionPage.verifyOrderCreated();
		submissionPage.verifyAccountNumberCreated();
		submissionPage.verifyThankYouMessage();
	}
	
	public void getDriverPaypal() {
		Page driverPaypal = paymentPage.openPaypalPage();
		paymentPage = new SelfCarePaymentPage(driverPaypal);
		DriverSteps.setActiveDriver(driverPaypal);
	}
	
	public void setDefaultDriver() {
		driver.bringToFront();
		paymentPage = new SelfCarePaymentPage(driver);
		DriverSteps.setActiveDriver(driver);
	}
}

