package com.stepDefinition;

import java.io.IOException;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.microsoft.playwright.Page;
import com.pages.CsmartAccountPage;
import com.pages.CsmartSalesOrderPage;
import com.user.UserDetails;

import io.cucumber.java.en.And;

public class CsmartServiceSteps {
	
	private Page driver;
    UserDetails userDetails;
	String tenant = System.getProperty("tenant");
	private CsmartSalesOrderPage salesOrderPage;
	private CsmartAccountPage accountPage;
	
	public CsmartServiceSteps(DriverSteps driverSteps) {
		driver = driverSteps.getDriver();
		salesOrderPage = new CsmartSalesOrderPage(driver);
		accountPage = new CsmartAccountPage(driver);
	}
	
	@And("Verify that order number is in {string} status in Csmart")
	public void verify_order_number_status(String status) {
		salesOrderPage.validateOrderStatus(status);
	}
	
	@And("Agent creates new customer account")
	public void agent_creates_new_customer_account() {
		accountPage.createCustomerAccount();
	}
	
	@And("Enters customer details")
	public void enters_customer_details() throws StreamReadException, DatabindException, IOException {
		accountPage.enterCustomerDetails(DriverSteps.getEnvProperty("customer.details"));
		accountPage.getAccountNumber();
	}
	
	@And("Customer account is {string}")
	public void customer_account_status(String accountStatus) {
		accountPage.validateAccountStatus(accountStatus);
	}

}