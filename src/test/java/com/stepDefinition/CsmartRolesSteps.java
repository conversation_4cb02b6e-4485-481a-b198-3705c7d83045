package com.stepDefinition;

import java.io.IOException;
import java.util.List;

import org.junit.Assert;
import org.slf4j.Logger;

import com.enums.CsmartRoles;
import com.enums.CsmartTenant;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.init.LogInitializer;
import com.microsoft.playwright.Page;
import com.pages.CsmartLoginPage;
import com.pages.RolePage;
import com.role.Privileges;
import com.role.RoleDetails;

import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;

public class CsmartRolesSteps {

	private Logger log;
	private Page driver;
    private CsmartLoginPage login;
    private RolePage role;
	String tenant = System.getProperty("tenant");
	private int dataSetID;
	
	public CsmartRolesSteps(DriverSteps driverSteps) {
		driver = driverSteps.getDriver();
		log = LogInitializer.getLogger(CsmartRolesSteps.class);
		login = new CsmartLoginPage(driver);
		role = new RolePage(driver);
	}
	
	RoleDetails rolesDetails;
	

	@Given("User {string} with role {string} logs into Csmart Application")
	public void role_logs_into_csmart_application(String userData, String role) throws IOException {
		log.info("Inside User logs into Csmart Application");
		this.dataSetID = Integer.parseInt(userData) - 1;
		String username = null;
		String password = null;
		login.setCsmartURL(DriverSteps.getEnvProperty("csmart.url"));
		login.enterURL();
		driver.waitForTimeout(3000);
		
		if (CsmartTenant.SYMBIO.getDescription().equalsIgnoreCase(tenant)) {
			
			switch (CsmartRoles.fromDescription(role)) {

			case SUPER_ADMIN:	
				username = DriverSteps.getEnvProperty("csmart.superadmin.username");
				password = DriverSteps.getEnvProperty("csmart.superadmin.password");
				break;
			case PLATFORM_HEAD:	
				username = DriverSteps.getEnvProperty("csmart.platform.username");
				password = DriverSteps.getEnvProperty("csmart.platform.password");
				break;
				
			case COMPLIANCE_HEAD:	
				username = DriverSteps.getEnvProperty("csmart.compliance.username");
				password = DriverSteps.getEnvProperty("csmart.compliance.password");
				break;
				
			case OPERATIONS_HEAD:	
				username = DriverSteps.getEnvProperty("csmart.operations.username");
				password = DriverSteps.getEnvProperty("csmart.operations.password");
				break;
			case L3_AGENT:	
				username = DriverSteps.getEnvProperty("csmart.agent.username");
				password = DriverSteps.getEnvProperty("csmart.agent.password");
				break;
				
			default:
				Assert.fail("No Role Associated with the selected tenant");
			}
			
		} else if (CsmartTenant.ALDI_MOBILE.getDescription().equalsIgnoreCase(tenant)) {
			
			switch (CsmartRoles.fromDescription(role)) {

			case SUPER_ADMIN:	
				username = DriverSteps.getEnvProperty("csmart.superadmin.username");
				password = DriverSteps.getEnvProperty("csmart.superadmin.password");
				break;
				
			case PLATFORM_HEAD:	
				username = DriverSteps.getEnvProperty("csmart.platform.username");
				password = DriverSteps.getEnvProperty("csmart.platform.password");
				break;
				
			case COMPLIANCE_HEAD:	
				username = DriverSteps.getEnvProperty("csmart.compliance.username");
				password = DriverSteps.getEnvProperty("csmart.compliance.password");
				break;
				
			case PRODUCT_HEAD:	
				username = DriverSteps.getEnvProperty("csmart.producthead.username");
				password = DriverSteps.getEnvProperty("csmart.producthead.password");
				break;
				
			case FINANCE_HEAD:	
				username = DriverSteps.getEnvProperty("csmart.financehead.username");
				password = DriverSteps.getEnvProperty("csmart.financehead.password");
				break;
				
			case PLANNING_HEAD:	
				username = DriverSteps.getEnvProperty("csmart.planning.username");
				password = DriverSteps.getEnvProperty("csmart.planning.password");
				break;
				
			case CONTENT_MANAGER:	
				username = DriverSteps.getEnvProperty("csmart.content.username");
				password = DriverSteps.getEnvProperty("csmart.content.password");
				break;
				
			case PRODUCT_MANAGER:	
				username = DriverSteps.getEnvProperty("csmart.productmanager.username");
				password = DriverSteps.getEnvProperty("csmart.productmanager.password");
				break;
				
			case FINANCE_MANAGER:	
				username = DriverSteps.getEnvProperty("csmart.financemanager.username");
				password = DriverSteps.getEnvProperty("csmart.financemanager.password");
				break;
				
			case SALES_MANAGER:	
				username = DriverSteps.getEnvProperty("csmart.sales.username");
				password = DriverSteps.getEnvProperty("csmart.sales.password");
				break;
				
			case LOGISTICS_MANAGER:	
				username = DriverSteps.getEnvProperty("csmart.logistic.username");
				password = DriverSteps.getEnvProperty("csmart.logistic.password");
				break;
				
			case AU_L3_AGENT:	
				username = DriverSteps.getEnvProperty("csmart.l3.username");
				password = DriverSteps.getEnvProperty("csmart.l3.password");
				break;
				
			case L2_AGENT:	
				username = DriverSteps.getEnvProperty("csmart.l2.username");
				password = DriverSteps.getEnvProperty("csmart.l2.password");
				break;
				
			case L1_AGENT:	
				username = DriverSteps.getEnvProperty("csmart.l1.username");
				password = DriverSteps.getEnvProperty("csmart.l1.password");
				break;
				
			default:
				Assert.fail("No Role Associated with the selected tenant");
			}
			
		} else  {
			
			Assert.fail("No Role Associated with the selected tenant");
		}
	
		login.verifyUserLoggedInCsmart(username, password);
	}

	@Then("User should have privileges based on the role")
	public void user_should_have_privileges_based_on_role() throws StreamReadException, DatabindException, IOException {
		
		String fileName = "testData/" + tenant + DriverSteps.getEnvProperty("privilege.path");
		log.info("File Name : {}", fileName);
		rolesDetails = role.getPrivileges(fileName);
	}
	
	@Then("Validates the users permission based on the privilege")
	public void validates_the_user_permission_based_on_privilege() throws IllegalArgumentException, IllegalAccessException {

		List<String> modulesList = role.getModuleList(rolesDetails,dataSetID);
		Privileges privileges = rolesDetails.getRoles()[dataSetID].getPrivileges().get(0);
		log.info("Logged in Role: {} ", rolesDetails.getRoles()[dataSetID].getRole());
	
		for (int i = 0; i < modulesList.size(); i++) {
			role.validatePermission(modulesList.get(i), privileges);
		}
	}
}