package com.stepDefinition;

import java.io.IOException;

import org.junit.Assert;

import com.customer.CustomerDetails;
import com.enums.AccountDetails;
import com.enums.PdfContents;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.file.WriteToFile;
import com.microsoft.playwright.Page;
import com.pages.SelfCareAccountPage;
import com.pages.SelfCareHomePage;
import com.pages.SelfCareLoginPage;
import com.pages.SelfCareSubmissionPage;
import com.utils.SkyHighUtil;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;

public class SelfCareCustomerSteps {
	
	private Page driver;
    private SelfCareLoginPage login;
    private SelfCareHomePage homePage;
    private SelfCareAccountPage accountPage;
    private int dataSetID;
    CustomerDetails customerDetails;
    private WriteToFile write;
   // private SelfCareInvoicePage invoice;

	public SelfCareCustomerSteps(DriverSteps driverSteps) {
		driver = driverSteps.getActiveDriver();
		login = new SelfCareLoginPage(driver);
		homePage = new SelfCareHomePage(driver);
		accountPage = new SelfCareAccountPage(driver);
		write = new WriteToFile();
		//invoice = new SelfCareInvoicePage(driver);
	}

	@And("Customer navigates to Self Care Website")
	public void customer_navigates_to_self_care_website() throws IOException {
		driver.reload();
		login.setSelfCareURL(DriverSteps.getEnvProperty("selfcare.url"));
		login.enterURL();
		login.verifyPageStatus();
		login.waitForPageToLoad();
		SkyHighUtil.takeScreenshot(driver, new Object(){}.getClass().getEnclosingMethod().getName());
	}
	
	@And("Customer is on SelfCare homepage")
	public void user_is_on_selfcare_homepage() {
		homePage.validateHomePage();
		homePage.verifyAccountNUmberDisplay();

	}
	
	@Given("{string} logs into Self Care Website") 
	public void user_logs_into_selfcare_website(String userDataId) throws IOException {
		this.dataSetID = Integer.parseInt(userDataId) - 1;
		login.setSelfCareURL(DriverSteps.getEnvProperty("selfcare.url"));
		login.enterURL();
		login.verifyPageStatus();
		login.waitForPageToLoad();
		String userName = GlobalVariables.getAccountNumber();
		login.setUserName(userName);
		login.validateLoginAccount();
		login.setPassword(GlobalVariables.getPassword());
		login.verifyUserLoggedIn();
	}
	
	@Then("User navigates to {string} page in SelfCare")
	public void user_navigates_to_page(String selfCareMenu) {

		homePage.setSelfcareMenu(selfCareMenu);
		homePage.selectSelfcareMenu();
	} 	
	
	
	@Then("User navigates to {string} details page")
	public void user_navigates_to_details(String accountDetailsMenu) {
		accountPage.selectAccountDetailsMenu(accountDetailsMenu);
	}
	
	@And("User verifies {string} details")
	public void user_should_be_able_to_verify_details(String accountDetails) throws StreamReadException, DatabindException, IOException {
		
		customerDetails = accountPage.getCustomerDetails(DriverSteps.getEnvProperty("customer.details"));
		switch (AccountDetails.fromDescription(accountDetails)) {
		
		case PERSONAL_INFORMATION:
			accountPage.verifyPersonalInformation(customerDetails.getTestData()[2].getData().get(dataSetID));
			break;
		case ADDRESS:
			accountPage.verifyAddressInformation(customerDetails.getTestData()[2].getData().get(dataSetID).getAddressDetails());
			break;
		case PAYMENT_INFORMATION:
			accountPage.verifyPaymentInformation(customerDetails.getTestData()[2].getData().get(dataSetID).getPaymentDetails());
			break;
		default:
			Assert.fail("Invalid details page option : " + accountDetails);
		}
	}
	
	@And("Service Number is {string} in Selfcare Application")
	public void service_number_in_selfcare(String serviceStatus) throws StreamReadException, DatabindException, IOException {

		homePage.selectServiceNumber(serviceStatus, GlobalVariables.getServiceNumber());
	}
	
	@Then("Write {string} details to file")
	public void write_details_to_file(String pdfContent) throws StreamReadException, DatabindException, IOException {

		String file = DriverSteps.getEnvProperty("invoice.file");
		switch (PdfContents.fromDescription(pdfContent)) {

		case CUSTOMER:
			
			if (customerDetails == null) {
				customerDetails = accountPage.getCustomerDetails(DriverSteps.getEnvProperty("customer.details"));
			}
			write.writeToFile(customerDetails.getTestData()[2].getData().get(dataSetID), file);
			break;

		case ACCOUNT:
			write.writeToFile(GlobalVariables.getAccountNumber(), file);
			write.writeToFile(SelfCareSubmissionPage.getOrderNumber(), file);
			break;
			
		default:
			Assert.fail("Invalid PDF Content: " + pdfContent);
			break;

		}
	}

}
		
	

		
