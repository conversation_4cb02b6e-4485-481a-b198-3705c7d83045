package com.stepDefinition;

import java.io.IOException;

import com.microsoft.playwright.Page;
import com.opencsv.exceptions.CsvValidationException;
import com.pages.CsmartBulkOperationPage;
import com.pages.CsmartInventoryPage;
import com.pages.CsmartLoginPage;
import com.pages.CsmartMessageOfTheDayPage;
import com.pages.CsmartRechargeVoucherPage;
import com.pages.CsmartTicketPage;
import com.user.UserDetails;

import io.cucumber.java.en.And;

public class CsmartInventoryStepsDefinition {

	private Page driver;
    private CsmartInventoryPage inventory;
    private CsmartRechargeVoucherPage rechargePage;
    private CsmartMessageOfTheDayPage motd;
    private CsmartTicketPage ticket;
    private CsmartBulkOperationPage bulkOperation;
    private CsmartLoginPage login;

	public CsmartInventoryStepsDefinition(DriverSteps driverSteps) {
		driver = driverSteps.getDriver();
		inventory = new CsmartInventoryPage(driver);
		rechargePage = new CsmartRechargeVoucherPage(driver);
		ticket = new CsmartTicketPage(driver);
		motd = new CsmartMessageOfTheDayPage(driver);
		login = new CsmartLoginPage(driver);
		bulkOperation = new CsmartBulkOperationPage(driver);
	}
	
    UserDetails userDetails;
	private int voucherCount;
	private String bulkAction;
	private String bypassApproval;
	private String resourceType;

	@And("Creates upload CSV file with {int} {string}")
	public void creates_csv_file(int resourceCount, String resourceType) throws CsvValidationException, IOException {
		this.resourceType = resourceType;
		inventory.createCSVFile(resourceCount, this.resourceType);
	}

	@And("Checks for duplicate entries using {string}")
	public void checks_data_for_duplicate_entries(String searchColumn) throws CsvValidationException, IOException {
		
		inventory.checkDuplicateEntries(this.resourceType, searchColumn);
	}
	
	@And("User uploads file in Import Inventory")
	public void user_uploads_file_in_import_inventory() throws CsvValidationException, IOException {
		
		inventory.setUserName(login.getUserName());
	    inventory.uploadImportFile(this.resourceType);
	}
	
	@And("User Enters import details")
	public void user_enters_import_details()  {
		inventory.enterImportDetails(this.resourceType);
	}
	
	@And("Use {string} as duplicate handling method")
	public void use_duplicate_method(String handlingMethod) throws CsvValidationException, IOException {

	    inventory.selectHandlingMethod(handlingMethod);
		inventory.submitImportPage();
	}

	@And("User changes status of {int} {string} in Inventory from {string} to {string}")
	public void user_voids_inventory(int productCount, String productType, String initialStatus, String newStatus) {
		
		inventory.setUserName(login.getUserName());
	    inventory.searchData(productType, initialStatus);
	    inventory.selectProduct(productType, productCount);
		inventory.voidProduct(productCount, initialStatus, newStatus);
	}
	
	@And("Validate that {string} status is changed to {string}")
	public void validate_status_is_changedy(String searchColumn, String newStatus) {
	    inventory.validateNewInventoryStatus(searchColumn, newStatus);
	}
	
	@And("User creates ticket for {int} Voucher Code PIN with {string} status")
	public void user_creates_ticket_voucher_code_pin(int voucherCount, String voucherStatus) {
		this.voucherCount = voucherCount;
	    ticket.enterTicketDetails(voucherCount, voucherStatus);
	}
	
	@And("User Bypass Approval is set to {string}")
	public void user_sets_bypass_approval(String bypassApproval) {
		this.bypassApproval = bypassApproval;
	    ticket.bypassApproval(bypassApproval);
	    ticket.submitTicket();
	    ticket.validateStatus(bypassApproval);
	}
	
	@And("User generates voucher in Inventory Catalogue")
	public void user_generates_voucher_in_inventory_catalogue() {
		rechargePage.generateVoucherPIN(ticket.getTicketNo(), login.getUserName());
	}
	
	@And("User {string} the voucher request")
	public void user_approve_the_voucher_request(String approvalStatus) {
		rechargePage.evaluateRequest(this.bypassApproval, ticket.getTicketNo(), approvalStatus);
	}
	
	@And("Validate that {string} are successfully imported with correct status")
	public void validate_successfully_imported_with_correct_status(String resourceType) {
	    inventory.validateImportedData(resourceType);
	}
	
	@And("Validate that voucher are added to Inventory Catalogue in {string} status")
	public void validate_voucher_added_inventory(String status) {
		rechargePage.validateGeneratedVoucher(status, ticket.getTicketNo(), voucherCount, login.getUserName());
	}
		
	@And("User creates MOTD with {string} Message Type and {string} status")
	public void user_creates_message_of_the_day(String messageType, String motdStatus) {
		motd.enterMessageOfTheDayDetails(messageType, System.getProperty("tenant"), motdStatus);
		motd.validateMotdDetails();
	}
	
	@And("Validate that MOTD is displayed in Csmart Dashboard")
	public void validate_that_motd_is_displayed() {
		motd.validateDisplayCRM();
	}
	
	@And("Selects {int} {string} with {string} status for {string} Operation")
	public void user_selects_data_in_inventory_catalogue(int count, String productType, String initialStatus, String bulkAction) {
		 this.bulkAction = bulkAction;
		 inventory.setUserName(login.getUserName());
		 inventory.searchData(productType, initialStatus);
		 inventory.createCSVFile(count, productType, bulkAction);
		
	}
	
	@And("Validate {string} Bulk Operation status is {string}")
	public void validate_bulk_operation_status(String bulkAction, String status) {
		bulkOperation.validateOperationStatus(bulkAction, status);
	}
	
	@And("User performs Bulk Operation")
	public void user_performs_bulk_operation() {
		
		bulkOperation.selectBulkAction(this.bulkAction);
		bulkOperation.uploadFile(inventory.getFilenameMap());

	}

}

