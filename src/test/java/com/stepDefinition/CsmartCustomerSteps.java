package com.stepDefinition;

import java.io.IOException;
import org.junit.Assert;
import org.slf4j.Logger;
import com.enums.CsmartTenant;
import com.init.LogInitializer;
import com.microsoft.playwright.Page;
import com.pages.CsmartHomePage;
import com.pages.CsmartLoginPage;
import com.pages.CsmartManageUsersPage;
import com.pages.CsmartTenancyPage;
import com.user.UserDetails;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;

public class CsmartCustomerSteps {

	private Logger log;
	private Page driver;
    private CsmartLoginPage login;
    private CsmartHomePage homePage;
    private CsmartManageUsersPage manageUser;
    private CsmartTenancyPage tenancyPage;
    UserDetails userDetails;
	String tenant = System.getProperty("tenant");
	
	public CsmartCustomerSteps(DriverSteps driverSteps) {
		driver = driverSteps.getDriver();
		log = LogInitializer.getLogger(CsmartCustomerSteps.class);
		login = new CsmartLoginPage(driver);
		homePage = new CsmartHomePage(driver);
		manageUser = new CsmartManageUsersPage(driver);
		tenancyPage = new CsmartTenancyPage(driver);
	}
	
	@Given("Agent logs into Csmart Application")
	public void user_logs_into_csmart_application() throws IOException {
		log.info("Inside User logs into Csmart Application");

		String username = null;
		String password = null;
		login.setCsmartURL(DriverSteps.getEnvProperty("csmart.url"));
		login.enterURL();
		//login.verifyPageStatus();
		driver.waitForTimeout(3000);
		
		switch (CsmartTenant.fromDescription(tenant)) {

		case SYMBIO:	
			username = DriverSteps.getEnvProperty("csmart.enableradmin.username");
			password = DriverSteps.getEnvProperty("csmart.enableradmin.password");
			break;
		case MEDION:	
			username = DriverSteps.getEnvProperty("csmart.spadmin.username");
			password = DriverSteps.getEnvProperty("csmart.spadmin.password");
			break;
		case ALDI_MOBILE:	
			username = DriverSteps.getEnvProperty("csmart.sp.username");
			password = DriverSteps.getEnvProperty("csmart.sp.password");
			break;

		default:
			Assert.fail("Invalid Tenant");
		}
		
		login.verifyUserLoggedInCsmart(username, password);
	}
	
	@And("User logs out to Csmart Application")
	public void user_logs_out_csmart_application() {
		login.logoutApplication();
	}
	
	@And("User is on Csmart Applicaton homepage")
	public void user_is_on_csmart_application_homepage() {
		homePage.validateHomePage();
	}
	
	@And("User navigates to {string} {string} page in Csmart")
	public void user_navigates_page_csmart(String csmartOption, String submenu) {
		homePage.selectDashboardOption(csmartOption);
		homePage.selectSubMenu(submenu);
	}
	
	@And("User navigates to {string} page in Csmart")
	public void user_navigates_page_csmart(String module) {
		homePage.selectDashboardOption(module);
	}

	@And("User Navigates to {string} option under {string}")
	public void user_navigates_option(String subModule, String module) {
		manageUser.navigateCrmModule(module, subModule);
	}
	
	@And("Validate that CSV Header is set to {string}")
	public void user_navigates_option(String csvHeader) {
		tenancyPage.validateCSVHeader(csvHeader, tenant);
	}
}