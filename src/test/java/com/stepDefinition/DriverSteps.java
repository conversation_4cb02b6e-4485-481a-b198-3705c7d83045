package com.stepDefinition;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.ResourceBundle;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import com.enums.Browsers;
import com.file.WriteToFile;
import com.init.LogInitializer;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.Tracing;
import com.opencsv.exceptions.CsvValidationException;
import com.pages.SelfCareSubmissionPage;
import com.stepDefinition.DriverSteps;
import com.utils.SkyHighUtil;

import io.cucumber.java.After;
import io.cucumber.java.Before;
import io.cucumber.java.Scenario;

public class DriverSteps {
	
	private Playwright playwright;
	private BrowserContext context;
	private Browser browser;
	private static ThreadLocal<Page> driver = new ThreadLocal<Page>();
	private static ThreadLocal<Page> activeDriver = new ThreadLocal<Page>();
	private Logger log = LogInitializer.getLogger(DriverSteps.class);
	private static ResourceBundle envProperty;
	static Properties prop;
    protected static String testEnvironment;
    private String trace;
    static String scenario;
    
	public static String getScenario() {
		return scenario;
	}

	public static void setScenario(String scenario) {
		DriverSteps.scenario = scenario;
	}

	private static ThreadLocal<Boolean> skipFlag = new ThreadLocal<Boolean>() {
		@Override
		protected Boolean initialValue() {
			return false;
		}
	};

	public Page getDriver() {
		return TestContext.getPage();
	}
	
	public static void setActiveDriver(Page driver) {
		
		activeDriver.set(driver);
	}
	
	public Page getActiveDriver() {
		return activeDriver.get();
	}
	

	
	@Before
	public void setup(Scenario scenario) throws CsvValidationException, IOException {
		setScenario(scenario.getName());
		log.info("SCENARIO: {}",  scenario.getName().toUpperCase());
		log.info("Previous skipFlag status: {}",  skipFlag.get().toString());
		if ("true".contentEquals(skipFlag.get().toString())){
			log.error("Prerequisite scenario failed!!! Aborting test!");
		}
		assertThat("Creation or activation failed, aborting test!!!", skipFlag.get(), equalTo(false));
		prop = getPropertyData();

		String browserName = getDataConfig("browser").toLowerCase();
		trace = getDataConfig("trace");
		log.info("Browser Name from properties: {}", browserName);
		TestContext.setScenario(scenario);
		playwright = Playwright.create();

			switch (Browsers.fromDescription(browserName)) {
			
			case CHROME:
				browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(false));
				break;
				
			case FIREFOX:
				browser = playwright.firefox().launch(new BrowserType.LaunchOptions().setHeadless(true));
				break;
				
			case EDGE:
				browser = playwright.chromium()
				.launch(new BrowserType.LaunchOptions().setChannel("msedge").setHeadless(true));
				break;
				
			case HCHROME:
				browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(true));
				break;
			default:
				browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(true));
			}
			
			context = browser.newContext(new Browser.NewContextOptions()
					.setIgnoreHTTPSErrors(true)
					.setAcceptDownloads(true)
					.setViewportSize(1600, 1024));
			
			context.grantPermissions(getPermissions());
			context.setDefaultTimeout(60000);
			
			if ("On".equalsIgnoreCase(trace)) 
				context.tracing().start(new Tracing.StartOptions()
						  .setScreenshots(true)
						  .setSnapshots(true)
						  .setSources(true));
			
	    envProperty = ResourceBundle.getBundle("envConfig." + getTenant() + "_" + getEnvironment().toLowerCase());	
		driver.set(context.newPage());
		TestContext.setPlaywright(playwright);
		TestContext.setBrowser(browser);
		TestContext.setBrowserContext(context);
		TestContext.setPage(driver.get());
		
		setActiveDriver(driver.get());
		SelfCareSubmissionPage.setOrderNumber("");
	}
	
	private String getEnvironment() {
		String env = getDataConfig("env");
	    return StringUtils.isEmpty(env) ? "UAT" : env;
	}
	
	private String getTenant() {
	    String tenant = getDataConfig("tenant");
	    return StringUtils.isEmpty(tenant) ? "aldimobile" : tenant;
	}
	

	@After
	public void tearDown(Scenario scenario) {
		log.info(scenario.getName() + ": " + scenario.getStatus().toString().toUpperCase());
		log.info("Scenario tags: {}", scenario.getSourceTagNames());
		log.info("Scenario failed: {}" , scenario.isFailed());
		
		scenario.log(scenario.getName() + ": " + scenario.getStatus().toString().toUpperCase() + "\nAccount number: "
				+ GlobalVariables.getAccountNumber() + "\nService number: " + GlobalVariables.getServiceNumber()
				+ "\nOrder ID: " + SelfCareSubmissionPage.getOrderNumber());
		 
		if (scenario.isFailed() && skipFlag.get() == false) {
			
			byte[] screenshot = null;
			try {
				screenshot = getActiveDriver().screenshot(new Page.ScreenshotOptions()
						  .setPath(Paths.get("src/test/resources/screenshots/" + SkyHighUtil.getCurrentDateTime("yyyy-MM-dd") + "/" + "FailedScreenshot.png"))
						  .setFullPage(true));
				scenario.attach(screenshot, "image/png", "Failure screenshot");
			} catch (Exception e) {
				scenario.log("Failed to take screenshot!");
				log.error("Failed to take screenshot!");
				e.printStackTrace();
			}
			
			if ((scenario.getSourceTagNames().contains("@skipCheck"))) {
				log.error("Prerequisite scenario failed!!! Exiting feature and failing the rest of the scenarios!");
				skipFlag.set(true);
				log.info("Skipflag value: {}" , skipFlag.get());
			}
			
			if (StringUtils.isNotEmpty(WriteToFile.getFilePath())) {
				SkyHighUtil.deleteCSVFile(FileSystems.getDefault().getPath(WriteToFile.getFilePath()));
				log.info("Charge File was deleted..");
			}
			 
		}

		if ("On".equalsIgnoreCase(trace)) 
			context.tracing().stop(new Tracing.StopOptions().setPath(Paths.get("trace.zip")));
		
		if (driver != null) {
			driver.get().close();
		}
		if (context != null) {
			context.close();
		}
		if (browser != null) {
			browser.close();
		}
		if (playwright != null) {
			playwright.close();
		}
		TestContext.remove();
		
	}	
	
    protected static String getEnvProperty(String key) {
        return (envProperty.getString(key));
    }
    
    protected static Properties getPropertyData() throws IOException {
    	
    	FileInputStream fis = new FileInputStream(System.getProperty("user.home").replace("\\", "/") + "/workspace/skyhigh/skyhigh.dataconfig.properties");
		prop = new Properties();
		prop.load(fis);
		return prop;
    }
    
    protected static String getDataConfig(String key) {
        return prop.getProperty(key);
    }
    
	private List<String> getPermissions() {
		List<String> permissions = new ArrayList<String>();
		permissions.add("geolocation");
		return permissions;
	}
}
