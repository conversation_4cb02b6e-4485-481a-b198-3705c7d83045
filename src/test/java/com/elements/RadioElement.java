package com.elements;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;

public class RadioElement extends Element {

	private Locator element;

	public RadioElement(Page driver, Locator element) {
		super(driver);
		this.element = element;
	}

	public void click() {
		log.info("Click radio button: " + element.getAttribute("id"));
		element.check();
		driver.waitForCondition(() -> blockOverlay.isHidden());
		driver.waitForCondition(() -> blockPage.isHidden());
	}
}
