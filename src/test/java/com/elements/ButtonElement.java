package com.elements;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;

public class ButtonElement extends Element {

	private Locator element;
	private boolean dialogExpected;

	public ButtonElement(Page driver, Locator element) {
		super(driver);
		this.element = element;
		this.dialogExpected = false;
	}

	public ButtonElement(Page driver, Locator element, boolean dialogExpected) {
		super(driver);
		this.element = element;
		this.dialogExpected = dialogExpected;
	}

	public void click() {
		log.info("Click button: " + element.textContent().trim());
		if (this.dialogExpected) {
			log.info("Accepting dialog");
			driver.onDialog(dialog -> dialog.accept());
		}
		element.waitFor();
		element.click();
	}

}
