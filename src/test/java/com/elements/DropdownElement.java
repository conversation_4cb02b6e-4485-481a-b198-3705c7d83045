package com.elements;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.SelectOption;
import com.microsoft.playwright.options.WaitForSelectorState;

public class DropdownElement extends Element {

	private Locator element;

	public DropdownElement(Page driver, Locator element) {
		super(driver);
		this.element = element;
	}

	public void click() {
		log.info("Click dropdown: " + element.getAttribute("id"));
		element.click();
	}

	public static DropdownElement dropdownElement(Page driver, Locator element) {
		return new DropdownElement(driver, element);
	}
	
	public void select2DropdownSelect(Locator searchInput, String value) {
		try {
			element.waitFor(new Locator.WaitForOptions().setTimeout(30000));
			element.click();
			searchInput.waitFor(new Locator.WaitForOptions().setTimeout(30000));
			searchInput.fill(value);

			String optionSelector = String
					.format("xpath=//div[contains(@class, 'select2-result-label') and normalize-space()='%s']", value);

			Locator option = driver.locator(optionSelector);
			option.waitFor(new Locator.WaitForOptions().setTimeout(30000));
			option.click();
			driver.keyboard().press("Escape");

		} catch (Exception e) {
			log.error(e.getMessage());
		}
	}
	
	public void select2DropdownDialogBox(Locator searchInput, String value) {
		   try {
		        element.click();
		        searchInput.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
		        searchInput.fill(value);
		        String optionSelector = String.format("div.select2-result-label:has-text('%s')", value);
		        Locator option = driver.locator(optionSelector);
		        option.nth(0).click();
		    } catch (Exception e) {
		        log.error("Dropdown selection failed", e);
		        throw e;
		    }
		}

	public void selectByValue(String value) {
		element.click();
		log.info("Select dropdown value: " + value);
		element.selectOption(value);
	}

	public void selectByIndex(int index) {
		element.waitFor();
		element.click();
		log.info("Select dropdown index: " + index);
		element.selectOption(new SelectOption().setIndex(index));
		driver.waitForCondition(() -> blockOverlay.isHidden());
		driver.waitForCondition(() -> blockPage.isHidden());
	}

	public void selectByVisibleText(String text) {
		element.click();
		log.info("Select dropdown by visible text: " + text);
		element.selectOption(new SelectOption().setLabel(text));
		log.info("Dropdown selected: " + text);
		driver.waitForCondition(() -> blockOverlay.isHidden());
		driver.waitForCondition(() -> blockPage.isHidden());
	}
	

	public String getFirstSelectedOption() {
		String selectedElement = (String) element.evaluate("el => el.options[el.selectedIndex].text");
		log.info("Selected option visible text: " + selectedElement);
		return selectedElement;
	}

	public String getFirstSelectedAttribute(String attribute) {
		String selectedAttribute = null;
		if ("value".equalsIgnoreCase(attribute))
			selectedAttribute = element.inputValue();
		log.info("Selected option " + attribute + " : " + selectedAttribute);
		return selectedAttribute;
	}

	public int getDropdownSize() {
		driver.waitForTimeout(3000); // to wait for the next dropdown to populate
		int dropdownSize = element.count();
		log.info("Dropdown count: " + dropdownSize);
		return dropdownSize;
	}

}