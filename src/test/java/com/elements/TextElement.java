package com.elements;

import static com.microsoft.playwright.assertions.PlaywrightAssertions.assertThat;

import org.openqa.selenium.JavascriptExecutor;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;

public class TextElement extends Element {

	private Locator element;

	public TextElement(Page driver, Locator element) {
		super(driver);
		this.element = element;
	}

	public void checkVisibility() {
		log.info("Check visibility of element: " + element.getAttribute("id"));
		element.waitFor();
		assertThat(element).isVisible();
	}

	public String getText() {
		String text = element.innerText().trim();
		log.info("Text: " + text);
		return text;
	}

	public String getAttribute(String attribute) {
//		locator.waitFor(); //will not get values for hidden locators
		String attrib = element.getAttribute(attribute);
		log.info("Attribute: " + attrib);
		return attrib;
	}
	
	public String getHiddenTextElement(String id) {
		String hiddenText = ((JavascriptExecutor)driver).executeScript("return document.getElementById('" + id + "').value").toString();
		log.info("Hidden Text: " + hiddenText);
		return hiddenText;
	}
}
