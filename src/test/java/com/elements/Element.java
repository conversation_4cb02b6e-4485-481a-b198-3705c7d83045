package com.elements;

import org.slf4j.Logger;

import com.init.LogInitializer;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;

public class Element {

	protected Page driver;
	protected static Logger log;
	protected Locator blockOverlay;
	protected Locator blockPage;

	public Element(Page driver) {
		this.driver = driver;
		log = LogInitializer.getLogger(Element.class);
		//this.blockOverlay = driver.locator("//div[contains(@class, 'blockUI')]");
		this.blockOverlay = driver.locator("//div[contains(@class, 'blockUI blockOverlay')]");
		this.blockPage = driver.locator("//div[contains(@class, 'blockUI blockMsg blockPage')]");
	}
}
