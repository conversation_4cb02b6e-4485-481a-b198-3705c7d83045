package com.elements;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.TimeoutError;

public class TextfieldElement extends Element {

	private Locator element;

	public TextfieldElement(Page driver, Locator element) {
		super(driver);
		this.element = element;
	}

	public void setValue(String value) {
		log.info("Enter value in textfield: " + value);
		element.waitFor();
		element.fill(value);
	}

	public void setSensitiveValue(String value) {
		try {		
			element.waitFor();
			element.click();
			element.fill(value);
		} catch (TimeoutError te) {
			//enter code here to enter value via javascript
			log.info("Using java script to input value on sensitive element");
			this.element.evaluate("(node, value) => node.value = value", value);
		}
	}

	public void setValueOnReadonly(String value) {
		log.info("Using java script to input value on readOnly element");
		log.info("Enter value in textfield: " + value);
//		JavascriptExecutor executor = (JavascriptExecutor) this.driver;
//		executor.executeScript("arguments[0].value=arguments[1]", this.driver, value);
		this.element.evaluate("(node, value) => node.value = value", value);
	}

	public void setValueByChar(String value) {
		log.info("Enter value in textfield per character: " + value);
		for (char c : value.toCharArray()) {
			//element.sendKeys(String.valueOf(c));
			element.type(String.valueOf(c));
			driver.waitForCondition(() -> blockOverlay.isHidden());
			driver.waitForCondition(() -> blockPage.isHidden());
		}
	}

	public void setValueByKeyboard(String value) {
		log.info("Enter value in textfield by keyboard: " + value);
		element.click();
		driver.keyboard().type(value);
	}
	
	public void setValueByColor(String value) {
		log.info("Select Color Value " + value);
		   driver.evaluate("([element, value]) => {" +
			          "element.value = value;" +
			          "element.dispatchEvent(new Event('input', { bubbles: true }));" +
			          "element.dispatchEvent(new Event('change', { bubbles: true }));" +
			        "}", new Object[]{ element.elementHandle(), value });
	}
}
