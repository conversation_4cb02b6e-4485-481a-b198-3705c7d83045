package com.utils;


import com.init.LogInitializer;
import com.microsoft.playwright.options.Proxy;

import org.zaproxy.clientapi.core.ApiResponse;
import org.zaproxy.clientapi.core.ClientApi;
import org.zaproxy.clientapi.core.ClientApiException;
import org.zaproxy.clientapi.core.ApiResponseElement;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;
import java.util.ResourceBundle;

import org.slf4j.Logger;


public class ZapUtil {
	
	private static ClientApi clientApi;
	public static Proxy proxy;
	
	//OWASP ZAP items
	private static final String ZAP_PROXY_ADDRESS = "localhost";
	private static final int ZAP_PROXY_PORT = 8080;
	private static final String ZAP_API_KEY = "t0sos9nto7cil8oa38q5aqt5jj";
	
	private static final String TARGET_URL = getEnvProperty("csmart.url");
	private static ResourceBundle envProperty;
	static Properties prop;
	private static Logger log = LogInitializer.getLogger(ZapUtil.class);
	
	static {
		String proxyServerUrl = ZAP_PROXY_ADDRESS + ":" + ZAP_PROXY_PORT;
		clientApi = new ClientApi(ZAP_PROXY_ADDRESS, ZAP_PROXY_PORT, ZAP_API_KEY);
		proxy = new Proxy(proxyServerUrl);
	}
	
	public static void runZapPassiveScan() {
		try {
			ApiResponse apiResponse = clientApi.pscan.recordsToScan();
			clientApi.pscan.setEnabled("true");
			String tempVal = ((ApiResponseElement)apiResponse).getValue();
			log.info("---ZAP---Passive Scan is in progress");
			while(!tempVal.contentEquals("0")) {
				apiResponse = clientApi.pscan.recordsToScan();
				tempVal = ((ApiResponseElement)apiResponse).getValue();
			}
			log.info("---ZAP---Passive Scan is completed");
		} catch (ClientApiException e) {
			e.printStackTrace();
		}
	}
	
	public static void runZapActiveScan() {
		try {
			log.info("---ZAP---Active Scanning Target: {}", TARGET_URL);
			ApiResponse apiResponse = clientApi.ascan.scan(TARGET_URL, "True", "False", null, null, null);
			String scanId;
			int progress;
			
			//The scan now returns a scan ID to support concurrent scanning
			scanId = ((ApiResponseElement)apiResponse).getValue();
			
			//Poll the status until it completes
			while (true) {
				Thread.sleep(60000);
				progress = Integer.parseInt(clientApi.ascan.status(scanId).toString());
				log.info("Active Scan progress : {} %", progress);
				if (progress >= 100) {
					break;
				}
			}
			
			log.info("---ZAP---Active Scan Complete");
		} catch (Exception e) {
			log.info("Exception: {}",  e.getMessage());
			e.printStackTrace();
		}
	}
	
	public static void runZapSpiderScan() {
		try {
			log.info("---ZAP---Spider Scanning Target: {} ", TARGET_URL);
			ApiResponse apiResponse = clientApi.spider.scan(TARGET_URL, null, null, null, null);
			String scanId;
			int progress;
			
			//The scan now returns a scan ID to support concurrent scanning
			scanId = ((ApiResponseElement)apiResponse).getValue();
			
			//Poll the status until it completes
			while (true) {
				Thread.sleep(3000);
				progress = Integer.parseInt(clientApi.spider.status(scanId).toString());
				log.info("---ZAP---Spider Scan progress : {} %", progress);
				if (progress >= 100) {
					break;
				}
			}
			
			log.info("---ZAP---Spider Scan Complete");
		} catch (Exception e) {
			log.info("Exception: {}", e.getMessage());
			e.printStackTrace();
		}		
	}
	
	
	public static void generateZapReport(String feature, String scenario) {
		log.info("---ZAP---Generate Zap Security Report");
		if (clientApi != null) {
			String title = "Octane ZAP Security Report - " + feature;
			String template = "traditional-html";
			String description = "This is the Octane Zap Security Test Report for " + feature + ": " + scenario;
			String reportfilename = "zap-report-octane-" + SkyHighUtil.getCurrentDateTime("yyyyMMddHHmmss") + "-" + feature + "-" + scenario + ".html"; 
			String targetFolder = System.getProperty("user.dir");
			
			try {
				ApiResponse response = clientApi.reports.generate(title, template, null, description, null, null, null, null, null, reportfilename, null, targetFolder, null);
				log.info("ZAP report generated at this location: {} ", response.toString());
			} catch (ClientApiException e) {
				e.printStackTrace();
			}		
		}
	}
	
    protected static String getEnvProperty(String key) {
        return (envProperty.getString(key));
    }
    
    protected static Properties getPropertyData() throws IOException {
    	
    	FileInputStream fis = new FileInputStream(System.getProperty("user.home").replace("\\", "/") + "/skyhigh/skyhigh.dataconfig.properties");
		prop = new Properties();
		prop.load(fis);
		return prop;
    }
    
    protected static String getDataConfig(String key) {
        return prop.getProperty(key);
    }

}
