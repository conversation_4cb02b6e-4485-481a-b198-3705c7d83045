package com.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.SecureRandom;

import org.slf4j.Logger;
import org.junit.Assert;

import com.init.LogInitializer;
import com.inventory.InventoryDetails;
import com.microsoft.playwright.Page;
import com.enums.ResourceType;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import com.opencsv.exceptions.CsvException;
import com.opencsv.exceptions.CsvValidationException;
import com.stepDefinition.DriverSteps;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

import javax.net.ssl.HttpsURLConnection;

public class SkyHighUtil {
	
	static FileReader filereader;
	private static Logger log = LogInitializer.getLogger(SkyHighUtil.class);

	public static Object readDetailsFromFile(String fileName, Class<?> className)throws StreamReadException, DatabindException, IOException {
		ObjectMapper mapper = new ObjectMapper();
		File file = new File(fileName);
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		mapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		return mapper.readValue(file, className);
	}

	public static String convertDateFormat(String dateValue, String currentPattern, String newPattern) {
		String newDateValue = null;
		SimpleDateFormat currentDatePattern = new SimpleDateFormat(currentPattern);
		SimpleDateFormat newDatePattern = new SimpleDateFormat(newPattern);
		if (dateValue != null) {
			try {
				Date dbDate = currentDatePattern.parse(dateValue);
				newDateValue = newDatePattern.format(dbDate);
			} catch (ParseException e) {
				Assert.fail("Failed to convert date");
			}
		}
		return (newDateValue);
	}

	public static String convertDecimalPlaces(Double value, int places) {
		BigDecimal db = new BigDecimal(value).setScale(places, RoundingMode.HALF_UP);
		return String.valueOf(db);
	}

	public static String[] readDataFromCsvFile(String file, String data, String tenant) throws IOException, CsvException {
		String filePath = System.getProperty("user.home").replace("\\", "/") + file;
		boolean isDataFound = false;
		CSVReader reader = new CSVReader(new FileReader(filePath));
		String[] rowData = null;
		String[] column;
		int rowCount = 0;
		while ((column = reader.readNext()) != null) {
			if (column[4].equalsIgnoreCase("NEW") && column[0].equalsIgnoreCase(data) && column[1].equalsIgnoreCase(tenant)) {
				rowData = column;
				updateCSVFileRowValue("USED", rowCount, 4, filePath);
				isDataFound = true;
				break;
			}
			rowCount++;
		}	
		
		if (!isDataFound) {
			Assert.fail("No Data Found for: " + data.toUpperCase());
		}
		
		reader.close();
		return rowData;
	}

	public static void updateCSVFileRowValue (String value, int row, int col, String filePath) throws IOException, CsvException {
		CSVReader reader = new CSVReader(new FileReader(filePath));
		List<String[]> csvBody = reader.readAll();
		csvBody.get(row)[col] = value;
		reader.close();

		// Write to CSV file which is open
		CSVWriter writer = new CSVWriter(new FileWriter(filePath));
		writer.writeAll(csvBody);
		writer.flush();
		writer.close();
	}

	public static void delayRun(Integer minutes, Page driver) {
		
		for (int minute = 1; minute <= minutes; minute++) {
			driver.reload();
			log.info("Refresh: {}  minutes", minute );
			driver.waitForTimeout(60000);
			driver.waitForLoadState();
		}
	}
	
	public static int getResponseCode(String url) throws IOException {
		HttpsURLConnection connection = (HttpsURLConnection) new URL(url).openConnection();
		connection.setRequestMethod("HEAD");
		int responseCode = connection.getResponseCode();
		log.info("Response Code: {}",  responseCode);
		return responseCode;
	}
	

	public static String getCSVRowNum(String[] rowData, int number) {
		return rowData[number];
	}
	
	public static int generateRandomNumber(int range) {

		Random random = new Random();
		int numberRandom = random.nextInt(range);
		return numberRandom;
	}
	
	public static String generateRandomDigits(int length) {
	    StringBuilder sb = new StringBuilder();
	    SecureRandom random = new SecureRandom();
	    for (int i = 0; i < length; i++) {
	        sb.append(random.nextInt(10)); // digits 0–9
	    }
	    return sb.toString();
	}
	
	public static String generateRandomString(int length) {
		
		StringBuilder result = new StringBuilder();
		Random random = new Random();

		while (result.length() < length) {
            int ascii = random.nextInt(91 - 48) + 48; 

            if ((ascii >= 48 && ascii <= 57) || 
                (ascii >= 65 && ascii <= 90)) {
                result.append((char) ascii);
            }
        }

        return result.toString();
	}


	public static double subtractDouble(double a, double b) {
		BigDecimal c = BigDecimal.valueOf(a).subtract(BigDecimal.valueOf(b));
		return c.doubleValue();
	}

	public static String getCurrentDateTime(String pattern) {

	    ZonedDateTime auDateTime = ZonedDateTime.now(ZoneId.of("Australia/Sydney"));
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
		String currentDate = auDateTime.format(formatter);
		return currentDate;
	}
	
	public static String getFutureDateTime(String pattern, int addValue, String addedTo) {
		ZonedDateTime futureDateTime;
	    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
	    ZonedDateTime auDateTime = ZonedDateTime.now(ZoneId.of("Australia/Sydney"));

	    switch (addedTo.toLowerCase()) {
	    
        case "minute":
            futureDateTime = auDateTime.plusMinutes(addValue);
            break;
            
        case "hour":
            futureDateTime = auDateTime.plusHours(addValue);
            break;
            
        case "day":
            futureDateTime = auDateTime.plusDays(addValue);
            break;
            
        case "month":
            futureDateTime = auDateTime.plusMonths(addValue);
            break;
            
        default:
            throw new IllegalArgumentException("Unsupported time unit: " + addedTo);
    }

	    return futureDateTime.format(formatter);
	}
	
	public static List<String> convertToList(String filePath) throws IOException {
		List<String> result = new ArrayList<String>();
		BufferedReader br = null;
		try {
			
			br = new BufferedReader(new FileReader(filePath));
			String line;
			while  ((line = br.readLine()) != null) {
				result.add(line);
			}
		} catch (NullPointerException e) { // Handle the exception. }

		} finally {
			if (br != null) {
				br.close();
			}
		}
	
		return result;
	}
	
	public static void takeScreenshot(Page driver, String methodName) {
		driver.screenshot(new Page.ScreenshotOptions().setPath(Paths.get("src/test/resources/screenshots/" + getCurrentDateTime("yyyy-MM-dd") + "/"  + DriverSteps.getScenario() + "/" + methodName + ".png")).setFullPage(true));
	}

		
	
	public static List<InventoryDetails> extractResourcesFromCSV(String resourceType, String filePath) throws IOException, CsvValidationException {
		List<InventoryDetails> inventoryDetailsFromFile = new ArrayList<InventoryDetails>();
		InventoryDetails inventoryDetails = null;
	    try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
	        String[] column;

	        // Skip header
	        if ((column = reader.readNext()) == null) {
	            Assert.fail("CSV is empty or missing header");
	        }

	        int columnIndex = ResourceType.MSN.getDescription().equalsIgnoreCase(resourceType) ? 0 : 1; //sim number and msn
	        boolean isDataFound = false;

	        // Read data rows
	        while ((column = reader.readNext()) != null) {
	        	inventoryDetails = new InventoryDetails();

	        	if (!ResourceType.MSN.getDescription().equalsIgnoreCase(resourceType)) {
	        		inventoryDetails.setIccID(getCSVRowNum(column, 0).trim());
	        		
	        		if (ResourceType.ESIM.getDescription().equalsIgnoreCase(resourceType)) {
		        		inventoryDetails.setImsi(getCSVRowNum(column, 2).trim());
		        		inventoryDetails.setExpiryDate(getCSVRowNum(column, 3).trim());
	        			inventoryDetails.setActivationCode(getCSVRowNum(column, 4).trim());
	        			
	        		} else {
	             		inventoryDetails.setSimNumber(getCSVRowNum(column, 1).trim());
		        		inventoryDetails.setImsi(getCSVRowNum(column, 3).trim());
		        		inventoryDetails.setExpiryDate(getCSVRowNum(column, 4).trim());
	        		}
	        	} else {
		        	inventoryDetails.setMsn(getCSVRowNum(column, columnIndex).trim()); 
	        	}
	        	
	        	inventoryDetailsFromFile.add(inventoryDetails);
	            isDataFound = true;
	        }
	        if (!isDataFound) {
	            Assert.fail("No data to be uploaded");
	        }
	    }

	    return inventoryDetailsFromFile;
	}
	
	public static String getRandomHexColor() {
		int randomColor = generateRandomNumber(256);
		int r = randomColor;
		int g = randomColor;
		int b = randomColor;
		String hexColor = String.format("#%02x%02x%02x", r, g, b);
		log.info("Hex Color is {}", hexColor);
		return hexColor;
	}
	
	public static String generateFile(List<String> headerList, String fileName, List<List<String>> listItems) {
		log.info("Generate CSV File");
		String filePath = System.getProperty("user.home").replace("\\", "/") + fileName;
		
        try (FileWriter writer = new FileWriter(filePath)) {

            writer.write(String.join(",", headerList) + "\n");
            for (List<String> listItem : listItems) {
                writer.write(String.join(",", listItem) + "\n");
            }

		} catch (IOException e) {
			e.printStackTrace();
		}
		return filePath;
	}

	public static void deleteCSVFile (Path csvFilePath) {
		log.info("Delete CSV File");
		try {
			Files.delete(csvFilePath);
			log.info("File is successfully deleted:");
		} catch (NoSuchFileException x) {
			log.error("%s: no such" + " file or directory%n", csvFilePath);
		} catch (IOException x) {

		}
	}
}