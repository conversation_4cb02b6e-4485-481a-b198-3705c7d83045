package com.file;

import org.slf4j.Logger;

import com.customer.Data;
import com.init.LogInitializer;
import com.utils.SkyHighUtil;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;

public class WriteToFile {
	private Logger log = LogInitializer.getLogger(WriteToFile.class);

    public static ThreadLocal<String> filePath = new ThreadLocal<String>();

	public void writeToFile(Data data, String file) {
		String filePath = System.getProperty("user.home").replace("\\", "/") + file + SkyHighUtil.getCurrentDateTime("yyyy-MM-dd") + ".txt";
		WriteToFile.filePath.set(filePath);
		try (FileWriter fw = new FileWriter(filePath, true);
				BufferedWriter bw = new BufferedWriter(fw);
				PrintWriter out = new PrintWriter(bw)) {
			out.println(data.getPrimaryContactDetails().getFirstName());	
			out.println(data.getPrimaryContactDetails().getLastName());
			out.println(data.getPrimaryContactDetails().getMiddleName());
			out.println(data.getAddressDetails().getState());
			out.println(data.getAddressDetails().getStreetName());
			out.println(data.getAddressDetails().getStreetType());
			out.println(data.getAddressDetails().getSuburb());
			
			if (!"Not Required".equalsIgnoreCase(data.getAddressDetails().getUnitType())) {
				out.println(data.getAddressDetails().getUnitType());
			}

			out.println(data.getAddressDetails().getPostcode());
			out.println(data.getAddressDetails().getStreetNumber());

		} catch (IOException e) {
			log.warn("An error occurred in writing to file.");
			e.printStackTrace();
		}
		
	}
	
	public static String getFilePath() {
		return WriteToFile.filePath.get();
	}
	
	public static void setFilePath(String filePath) {
		WriteToFile.filePath.set(filePath);
	}

	public void writeToFile(String data, String file) {
		String filePath = System.getProperty("user.home").replace("\\", "/") + file + SkyHighUtil.getCurrentDateTime("yyyy-MM-dd") + ".txt";
		WriteToFile.filePath.set(filePath);
		
		log.info("Write Description to file: {}",  data);
	
		try (FileWriter fw = new FileWriter(filePath, true);
				BufferedWriter bw = new BufferedWriter(fw);
				PrintWriter out = new PrintWriter(bw)) {
			out.println(data);
		} catch (IOException e) {
			log.warn("An error occurred in writing to file.");
			e.printStackTrace();
		}
		
	}
}
