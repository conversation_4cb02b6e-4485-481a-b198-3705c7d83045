package com.enums;

public enum PaymentType {
	
	CREDIT_CARD("Credit Card"),
	VOUCHER_CODE("Voucher Code"),
	PARTIAL_CREDIT("Partial Credit"),
	FULL_CREDIT("Full Credit"),
	PAYPAL("Paypal");
	
	private String description;

	PaymentType(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static PaymentType fromDescription(String desc) {
        for (PaymentType type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
