package com.enums;

public enum AccountDetails {
	
	PERSONAL_INFORMATION("Personal Information"),
	PAYMENT_INFORMATION("Payment Information"),
	PASSWORD("Password"),
	TELEPHONE_SECURITY("Telephone Security"),
	ADDRESS("Address");

	private String description;

	AccountDetails(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static AccountDetails fromDescription(String desc) {
        for (AccountDetails type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
