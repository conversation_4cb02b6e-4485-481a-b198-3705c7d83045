package com.enums;

public enum BulkOperation {

	VOID_VOUCHER("Void Voucher"), 
	VOID_SIM("Void Sim"), 
	GENERATE_VOUCHER("Generate Voucher");

	private String description;

	BulkOperation(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static BulkOperation fromDescription(String desc) {
        for (BulkOperation type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
