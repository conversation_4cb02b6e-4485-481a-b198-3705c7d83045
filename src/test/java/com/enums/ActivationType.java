package com.enums;

public enum ActivationType {
	
	NEW("New"),
	PORT("Port");

	private String description;

	ActivationType(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static ActivationType fromDescription(String desc) {
        for (ActivationType type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
