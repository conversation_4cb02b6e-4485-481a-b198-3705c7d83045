package com.enums;

public enum CsmartDashboard {
	
	INVENTORY_CATALOGUE("System Admin"),
	CUSTOMERS_MANAGEMENT("System Admin");

	private String description;

	CsmartDashboard(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static CsmartDashboard fromDescription(String desc) {
        for (CsmartDashboard type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
