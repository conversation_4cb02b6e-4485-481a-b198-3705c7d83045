package com.enums;

public enum PlanSelection {
	
	DATA_PLAN("Data Plan"),
	FAMILY_PLAN_OWNER("Family PLan Owner"),
	FAMILY_PLAN_USER("Family PLan User"),
	VOUCHER_CODE("Voucher Code"),
	SKIP_FOR_NOW("Skip For Now"),
	MOBILE_PLAN("Mobile Plan");

	private String description;

	PlanSelection(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static PlanSelection fromDescription(String desc) {
        for (PlanSelection type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
