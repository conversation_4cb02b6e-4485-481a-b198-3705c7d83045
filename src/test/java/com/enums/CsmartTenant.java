package com.enums;

public enum CsmartTenant {

	ALDI_MOBILE("AldiMobile"),
	MEDION("Medion"),
	SYMBIO("Symbio");

	private String description;

	CsmartTenant(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static CsmartTenant fromDescription(String desc) {
        for (CsmartTenant type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
