package com.enums;

public enum CsmartUsers {

	SYSTEM_ADMIN("System Admin"),
	ENABLER_ADMIN("Enabler Admin"),
	SP_ADMIN("SP Admin"),
	SP("SP");

	private String description;

	CsmartUsers(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static CsmartUsers fromDescription(String desc) {
        for (CsmartUsers type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
