package com.enums;

public enum CustomerType {
	
	NEW("New"),
	FAMILY_PACK("Family Pack"),
	EXISTING_CUSTOMER("Existing");

	private String description;

	CustomerType(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static CustomerType fromDescription(String desc) {
        for (CustomerType type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
