package com.enums;

public enum CsmartRoles {

	SUPER_ADMIN("Super Admin"),
	PLATFORM_HEAD("Platform Head"),
	COMPLIANCE_HEAD("Compliance Head"),
	L3_AGENT("Symbio L3 Agent"),
	OPERATIONS_HEAD("Operations Head"),
	PRODUCT_HEAD("Product Head"),
	FINANCE_HEAD("Finance Head"),
	PLANNING_HEAD("Planning Head"),
	CONTENT_MANAGER("Content Manager"),
	FINANCE_MANAGER("Finance Manager"),
	PRODUCT_MANAGER("Product Manager"),
	SALES_MANAGER("Sales Manager"),
	LOGISTICS_MANAGER("Logistics Manager"),
	AU_L3_AGENT("AU L3 Agent"),
	L1_AGENT("L1 Agent"),
	L2_AGENT("L2 Agent");

	private String description;

	CsmartRoles(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static CsmartRoles fromDescription(String desc) {
        for (CsmartRoles type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Role is not valid for the tenant: " + desc);
    }
}
