package com.enums;

public enum CsmartModule {

	REPORTS("reports"),
	TICKETS("tickets"),
	CHOWN_DOMESTIC("chownDomesticViolence"),
	DOCUMENTS("documents"),
	VOUCHER("voucher"),
	PAYMENTS("payments"),
	USER_PERMISSION("userPermissions"),
	PRODUCT_CATALOGUES("productCatalogue"),
	INVENTORY("inventory"),
	CAMPAIGNS("campaigns"),
	TRANSLATIONS("translations"),
	FAQ("faq"),
	HRCT_SVC("hrctSVC"),
	VENDORS("vendors"),
	MOTD("motd"),
	CHOWN("chown"),
	STATUS_PAGE("statusPage"),
	NOTES_TEMPLATES("notesTemplates"),
	SALES_ORDER("salesOrder"),
	MNP("mnp"),
	CONTACTS("contacts"),
	ACCOUNTS("accounts"),
	INVOICE("invoice"),
	SECURITY_QUESTIONS("securityQuestions"),
	TRANSACTIONS("transactions"),
	TEMPLATES("templates");

	private String description;

	CsmartModule(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static CsmartModule fromDescription(String desc) {
        for (CsmartModule type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
