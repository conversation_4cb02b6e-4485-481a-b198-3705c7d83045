package com.enums;

public enum MessageType {

	CRITICAL("Critical"),
	INFORMA<PERSON>ON("Information"),
	WARNING("Warning"),
	DISCLAIMER("Disclaimer");

	private String description;

	MessageType(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static MessageType fromDescription(String desc) {
        for (MessageType type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
