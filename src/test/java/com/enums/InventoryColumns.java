package com.enums;

public enum InventoryColumns {
	
	PRODUCT_CATEGORY("Product Category"),
	INVENTORY_STATUS("Inventory Status"),
	VOUCHER_STATUS("Voucher Status"),
	SIM_NUMBER("Sim Number"),
	MSN("MSN"),
	ICCID("ICCID"),
	VOUCHER_CONTROL_ID("Voucher Control ID"),
	VOUCHER_CATEGORY("Voucher Category");
	
	private String description;

	InventoryColumns(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static InventoryColumns fromDescription(String desc) {
        for (InventoryColumns type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
