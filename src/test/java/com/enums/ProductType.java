package com.enums;

public enum ProductType {

	VOUCHER("Voucher"),
	SIM("Sim");

	private String description;

	ProductType(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static ProductType fromDescription(String desc) {
        for (ProductType type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
