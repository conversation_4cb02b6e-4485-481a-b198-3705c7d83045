package com.enums;

import java.util.Arrays;
import java.util.List;

public enum HeadersList {

	PHYSICALSIM("PHYSICALSIM") {
		public List<String> getHeaders() {
			return Arrays.asList("ICCID", "SIM NUMBER", "<PERSON>U<PERSON>", "IMS<PERSON>", "Expiry Date");
		}
	},
	ESIM("ESIM") {
		public List<String> getHeaders() {
			return Arrays.asList("ICCID", "PUK", "IMSI", "Expiry Date", "Activation Code");
		}
	},
	
	VOIDSIM("VOIDSIM") {
		public List<String> getHeaders() {
			return Arrays.asList("SIM Number");
		}
	},

	VOIDVOUCHER("VOIDVOUCHER") {
		public List<String> getHeaders() {
			return Arrays.asList("Voucher Control ID");
		}
	},
	
	GENERATEVOUCHER("GENERATEVOUCHER") {
		public List<String> getHeaders() {
			return Arrays.asList("Voucher Control ID");
		}
	},

	MSN("MSN") {
		@Override
		public List<String> getHeaders() {
			return Arrays.asList("MSN");
		}
	};

	public abstract List<String> getHeaders();

	private String description;

	HeadersList(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}

}
