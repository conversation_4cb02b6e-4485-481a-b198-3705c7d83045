package com.enums;

public enum PaymentOption {
	
	CREDIT_CARD("Credit Card"),
	PAYPAL("Paypal");

	private String description;

	PaymentOption(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static PaymentOption fromDescription(String desc) {
        for (PaymentOption type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
