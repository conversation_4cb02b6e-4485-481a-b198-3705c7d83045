package com.enums;

public enum Browsers {

	CHROME("chrome"), 
	FIREFOX("firefox"), 
	EDGE("edge"), 
	HCHROME("hchrome");

	private String description;

	Browsers(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static Browsers fromDescription(String desc) {
        for (Browsers type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
