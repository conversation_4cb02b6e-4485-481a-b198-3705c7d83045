package com.enums;

public enum ResourceType {

	PHYSICAL_SIM("Physical Sim"),
	ESIM("eSIM"),
	VOUCHER("Voucher"),
	MSN("MSN");


	private String description;

	ResourceType(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static ResourceType fromDescription(String desc) {
        for (ResourceType type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
