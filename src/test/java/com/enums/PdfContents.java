package com.enums;

public enum PdfContents {
	
	ACCOUNT("Account"),
	CHARGES("Charges"),
	CUSTOMER("Customer");

	private String description;

	PdfContents(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public static PdfContents fromDescription(String desc) {
        for (PdfContents type : values()) {
            if (type.getDescription().equalsIgnoreCase(desc)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with description " + desc);
    }
}
