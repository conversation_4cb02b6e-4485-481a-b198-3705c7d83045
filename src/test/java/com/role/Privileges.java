package com.role;

public class Privileges {

	Accounts accounts;
	Contacts contacts;
	SalesOrder salesOrder;
	Payments payments;
	Invoice invoice;
	Tickets tickets;
	Inventory inventory;
	Voucher voucher;
	Documents documents;
	ProductCatalogue productCatalogue;
	SecurityQuestions securityQuestions;
	Faq faq;
	Translations translations;
	Vendors vendors;
	Templates templates;
	Reports reports;
	Mnp mnp;
	HrctSvc hrctSVC;
	Motd motd;
	StatusPage statusPage;
	Chown chown;
	ChownDomesticViolence chownDomesticViolence;
	UserPermissions userPermissions;
	Campaigns campaigns;
	NotesTemplates notesTemplates;
	Transactions transactions;

	public Accounts getAccounts() {
		return accounts;
	}

	public void setAccounts(Accounts accounts) {
		this.accounts = accounts;
	}

	public Contacts getContacts() {
		return contacts;
	}

	public void setContacts(Contacts contacts) {
		this.contacts = contacts;
	}

	public SalesOrder getSalesOrder() {
		return salesOrder;
	}

	public void setSalesOrder(SalesOrder salesOrder) {
		this.salesOrder = salesOrder;
	}

	public Invoice getInvoice() {
		return invoice;
	}

	public void setInvoice(Invoice invoice) {
		this.invoice = invoice;
	}

	public Tickets getTickets() {
		return tickets;
	}

	public void setTickets(Tickets tickets) {
		this.tickets = tickets;
	}

	public Inventory getInventory() {
		return inventory;
	}

	public void setInventory(Inventory inventory) {
		this.inventory = inventory;
	}

	public Voucher getVoucher() {
		return voucher;
	}

	public void setVoucher(Voucher voucher) {
		this.voucher = voucher;
	}

	public Documents getDocuments() {
		return documents;
	}

	public void setDocuments(Documents documents) {
		this.documents = documents;
	}

	public ProductCatalogue getProductCatalogue() {
		return productCatalogue;
	}

	public void setProductCatalogue(ProductCatalogue productCatalogue) {
		this.productCatalogue = productCatalogue;
	}

	public SecurityQuestions getSecurityQuestions() {
		return securityQuestions;
	}

	public void setSecurityQuestions(SecurityQuestions securityQuestions) {
		this.securityQuestions = securityQuestions;
	}

	public Faq getFaq() {
		return faq;
	}

	public void setFaq(Faq faq) {
		this.faq = faq;
	}

	public Translations getTranslations() {
		return translations;
	}

	public void setTranslations(Translations translations) {
		this.translations = translations;
	}

	public Vendors getVendors() {
		return vendors;
	}

	public void setVendors(Vendors vendors) {
		this.vendors = vendors;
	}

	public Templates getTemplates() {
		return templates;
	}

	public void setTemplates(Templates templates) {
		this.templates = templates;
	}

	public Reports getReports() {
		return reports;
	}

	public void setReports(Reports reports) {
		this.reports = reports;
	}

	public Mnp getMnp() {
		return mnp;
	}

	public void setMnp(Mnp mnp) {
		this.mnp = mnp;
	}

	public HrctSvc getHrctSVC() {
		return hrctSVC;
	}

	public void setHrctSVC(HrctSvc hrctSVC) {
		this.hrctSVC = hrctSVC;
	}

	public Motd getMotd() {
		return motd;
	}

	public void setMotd(Motd motd) {
		this.motd = motd;
	}

	public StatusPage getStatusPage() {
		return statusPage;
	}

	public void setStatusPage(StatusPage statusPage) {
		this.statusPage = statusPage;
	}

	public Chown getChown() {
		return chown;
	}

	public void setChown(Chown chown) {
		this.chown = chown;
	}

	public ChownDomesticViolence getChownDomesticViolence() {
		return chownDomesticViolence;
	}

	public void setChownDomesticViolence(ChownDomesticViolence chownDomesticViolence) {
		this.chownDomesticViolence = chownDomesticViolence;
	}

	public UserPermissions getUserPermissions() {
		return userPermissions;
	}

	public void setUserPermissions(UserPermissions userPermissions) {
		this.userPermissions = userPermissions;
	}

	public Campaigns getCampaigns() {
		return campaigns;
	}

	public void setCampaigns(Campaigns campaigns) {
		this.campaigns = campaigns;
	}

	public Payments getPayments() {
		return payments;
	}

	public void setPayments(Payments payments) {
		this.payments = payments;
	}

	public NotesTemplates getNotesTemplates() {
		return notesTemplates;
	}

	public void setNotesTemplates(NotesTemplates notesTemplates) {
		this.notesTemplates = notesTemplates;
	}

	public Transactions getTransactions() {
		return transactions;
	}

	public void setTransactions(Transactions transactions) {
		this.transactions = transactions;
	}
}
