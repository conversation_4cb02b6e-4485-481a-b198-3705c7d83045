package com.role;

public class HrctSvc {
	
	boolean access;
	boolean skip;
	String sharingRule;
	String csmartMenu;
	String module;
	
	public String getSharingRule() {
		return sharingRule;
	}

	public void setSharingRule(String sharingRule) {
		this.sharingRule = sharingRule;
	}

	public String getCsmartMenu() {
		return csmartMenu;
	}

	public void setCsmartMenu(String csmartMenu) {
		this.csmartMenu = csmartMenu;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public boolean isAccess() {
		return access;
	}

	public void setAccess(boolean access) {
		this.access = access;
	}

	public boolean isSkip() {
		return skip;
	}

	public void setSkip(boolean skip) {
		this.skip = skip;
	}

}
