package com.role;

public class Voucher {
	
	boolean view;
	boolean create;
	boolean edit;
	boolean exportVoucher;
	boolean generateVoucher;
	boolean allocate;
	boolean bulkStatusUpdate;
	boolean voidVoucher;
	boolean activateVoucher;
	String sharingRule;
	String csmartMenu;
	String module;
	
	public String getSharingRule() {
		return sharingRule;
	}

	public void setSharingRule(String sharingRule) {
		this.sharingRule = sharingRule;
	}

	public String getCsmartMenu() {
		return csmartMenu;
	}

	public void setCsmartMenu(String csmartMenu) {
		this.csmartMenu = csmartMenu;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public boolean isView() {
		return view;
	}

	public void setView(boolean view) {
		this.view = view;
	}

	public boolean isCreate() {
		return create;
	}

	public void setCreate(boolean create) {
		this.create = create;
	}

	public boolean isEdit() {
		return edit;
	}

	public void setEdit(boolean edit) {
		this.edit = edit;
	}

	public boolean isExportVoucher() {
		return exportVoucher;
	}

	public void setExportVoucher(boolean exportVoucher) {
		this.exportVoucher = exportVoucher;
	}

	public boolean isGenerateVoucher() {
		return generateVoucher;
	}

	public void setGenerateVoucher(boolean generateVoucher) {
		this.generateVoucher = generateVoucher;
	}

	public boolean isAllocate() {
		return allocate;
	}

	public void setAllocate(boolean allocate) {
		this.allocate = allocate;
	}

	public boolean isBulkStatusUpdate() {
		return bulkStatusUpdate;
	}

	public void setBulkStatusUpdate(boolean bulkStatusUpdate) {
		this.bulkStatusUpdate = bulkStatusUpdate;
	}

	public boolean isVoidVoucher() {
		return voidVoucher;
	}

	public void setVoidVoucher(boolean voidVoucher) {
		this.voidVoucher = voidVoucher;
	}

	public boolean isActivateVoucher() {
		return activateVoucher;
	}

	public void setActivateVoucher(boolean activateVoucher) {
		this.activateVoucher = activateVoucher;
	}


}
