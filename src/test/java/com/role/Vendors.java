package com.role;

public class Vendors {
	
	boolean view;
	boolean create;
	boolean edit;
	String sharingRule;
	String csmartMenu;
	String module;
	
	public String getSharingRule() {
		return sharingRule;
	}

	public void setSharingRule(String sharingRule) {
		this.sharingRule = sharingRule;
	}

	public String getCsmartMenu() {
		return csmartMenu;
	}

	public void setCsmartMenu(String csmartMenu) {
		this.csmartMenu = csmartMenu;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public boolean isView() {
		return view;
	}

	public void setView(boolean view) {
		this.view = view;
	}

	public boolean isCreate() {
		return create;
	}

	public void setCreate(boolean create) {
		this.create = create;
	}

	public boolean isEdit() {
		return edit;
	}

	public void setEdit(boolean edit) {
		this.edit = edit;
	}


}
