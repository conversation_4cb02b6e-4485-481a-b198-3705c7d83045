package com.role;

public class Accounts {
	
	boolean view;
	boolean create;
	boolean edit;
	boolean purchaseAddOn;
	boolean recharge;
	boolean simSwap;
	boolean msnSwap;
	boolean terminate;
	String sharingRule;
	String csmartMenu;
	String module;

	public boolean isView() {
		return view;
	}

	public void setView(boolean view) {
		this.view = view;
	}

	public boolean isCreate() {
		return create;
	}

	public void setCreate(boolean create) {
		this.create = create;
	}

	public boolean isEdit() {
		return edit;
	}

	public void setEdit(boolean edit) {
		this.edit = edit;
	}

	public boolean isPurchaseAddOn() {
		return purchaseAddOn;
	}

	public void setPurchaseAddOn(boolean purchaseAddOn) {
		this.purchaseAddOn = purchaseAddOn;
	}

	public boolean isRecharge() {
		return recharge;
	}

	public void setRecharge(boolean recharge) {
		this.recharge = recharge;
	}

	public boolean isSimSwap() {
		return simSwap;
	}

	public void setSimSwap(boolean simSwap) {
		this.simSwap = simSwap;
	}

	public boolean isMsnSwap() {
		return msnSwap;
	}

	public void setMsnSwap(boolean msnSwap) {
		this.msnSwap = msnSwap;
	}

	public boolean isTerminate() {
		return terminate;
	}

	public void setTerminate(boolean terminate) {
		this.terminate = terminate;
	}

	public String getSharingRule() {
		return sharingRule;
	}

	public void setSharingRule(String sharingRule) {
		this.sharingRule = sharingRule;
	}

	public String getCsmartMenu() {
		return csmartMenu;
	}

	public void setCsmartMenu(String csmartMenu) {
		this.csmartMenu = csmartMenu;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

}
