image: maven:3.9.3-eclipse-temurin-17

stages:
  - test

cache:
  paths:
    - .m2/repository

before_script:
  - apt-get update && apt-get install -y wget gnupg unzip xvfb libnss3 libatk-bridge2.0-0 libgtk-3-0 libxss1 libasound2 libxcomposite1 libxdamage1 libxrandr2
  - wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
  - apt install -y ./google-chrome-stable_current_amd64.deb || apt --fix-broken install -y
  - google-chrome --version

test:
  stage: test
  script:
    - mvn test -Dcucumber.filter.tags="@selfcare" -Daccountnumber=01C2934456 -Denv=UAT -DserviceNumber=******** -Dbrowser=chrome -Dtrace=ON -Dtenant=aldimobile -Dpassword=Abcd@1234
  artifacts:
    when: always
    paths:
      - target/cucumber-reports/
      - target/surefire-reports/
    reports:
      junit: target/surefire-reports/TEST-*.xml
