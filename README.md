# SkyHigh Automation Framework
Playwright + Java + BDD (Cucumber) Framework for SkyHigh Application

## 1. Install Java JDK
Download and install JDK on your machine:  
[Oracle JDK Downloads](https://www.oracle.com/au/java/technologies/downloads/)

Make sure to set the `JAVA_HOME` environment variable and add `java` to your system PATH.

## 2. Install IDE (Eclipse or IntelliJ IDEA)
Choose your preferred Java IDE for development:

- **Eclipse:**  
  [Download Eclipse](https://www.eclipse.org/downloads/)

- **IntelliJ IDEA:**  
  [Download IntelliJ IDEA](https://www.jetbrains.com/idea/)

## 3. Clone SkyHigh Automation Project
Clone the existing SkyHigh automation repository from GitLab:

bash
 git clone https://gitlab.com/mnf-group/tiab/skyhigh/skyhigh-qa.git

## 4. Import Project [Eclipse / IntelliJ]

### Eclipse
- Open Eclipse IDE.
- Navigate to **File** > **Import**.
- Select **Maven** > **Existing Maven Projects**, then click **Next**.
- Browse to the cloned `skyhigh-qa` project directory.
- Ensure the project is selected, then click **Finish** to import.

### IntelliJ IDEA
- Open IntelliJ IDEA.
- Click on **File** > **Open**.
- Select the cloned `skyhigh-qa` project folder.
- IntelliJ will automatically detect it as a Maven project and import the dependencies.

### 5. Install git in local machine 
Windows: Open PowerShell as Administrator and run: choco install git
Mac: brew install git

### 6. Install Maven dependencies for Playwright, Java, and Maven
mvn -v
mvn clean install

### 7. Playwright Java dependencies, cucumber are typically managed in pom.xml
<dependency>
    <groupId>com.microsoft.playwright</groupId>
    <artifactId>playwright</artifactId>
    <version>1.49.0</version>
</dependency>
<dependency>
    <groupId>io.cucumber</groupId>
    <artifactId>cucumber-java</artifactId>
    <version>7.18.0</version>
</dependency>

### 8. Calling skyhigh.dataconfig.properties, skyhigh.qa.testdata.csv from local  
reference wiki url - https://mnfgroup.atlassian.net/wiki/spaces/TR/pages/81151655937/SkyHigh+Automation+Guidelines

### 9. Run test using Junit framework in command line
 run all tests: mvn test
 run tests with a specific tag: mvn test -Dcucumber.filter.tags="@smoke"

### 10. Debug test
mvn exec:java -e -D exec.mainClass=com.microsoft.playwright.CLI -D exec.args="codegen demo.playwright.dev/todomvc"

### 11. GitLab CI/CD pipeline
    gitlab-ci.yml
